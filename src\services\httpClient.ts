
import { getAuthToken, removeAuthToken } from './authService';
import { BASE_URL } from '@/config/env';

interface RequestOptions {
  headers?: Record<string, string>;
  [key: string]: any;
}

export const httpClient = {
  get: async (url: string, options: RequestOptions = {}) => {
    return makeRequest(url, { ...options, method: 'GET' });
  },

  post: async (url: string, data: any, options: RequestOptions = {}) => {
    return makeRequest(url, {
      ...options,
      method: 'POST',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
  },

  put: async (url: string, data: any, options: RequestOptions = {}) => {
    return makeRequest(url, {
      ...options,
      method: 'PUT',
      body: JSON.stringify(data),
      headers: {
        'Content-Type': 'application/json',
        ...options.headers
      }
    });
  },

  delete: async (url: string, options: RequestOptions = {}) => {
    return makeRequest(url, { ...options, method: 'DELETE' });
  },

  // For form data submissions (multipart/form-data)
  postForm: async (url: string, formData: FormData, options: RequestOptions = {}) => {
    return makeRequest(url, {
      ...options,
      method: 'POST',
      body: formData,
      // Do not set Content-Type for FormData, browser will set it with boundary
    });
  },

  // For form-urlencoded submissions
  postFormUrlEncoded: async (url: string, data: Record<string, string>, options: RequestOptions = {}) => {
    const formData = new URLSearchParams();

    // Add all key-value pairs to the form data
    Object.entries(data).forEach(([key, value]) => {
      formData.append(key, value);
    });

    return makeRequest(url, {
      ...options,
      method: 'POST',
      body: formData,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
        ...options.headers
      }
    });
  }
};

async function makeRequest(url: string, options: RequestOptions = {}) {
  const token = getAuthToken();

  const headers: Record<string, string> = {
    ...(options.headers || {})
  };

  // Add auth token if available
  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  } else {
    console.warn('No authentication token found. API request may fail if authentication is required.');
  }

  // Prepend BASE_URL if the URL doesn't start with http:// or https://
  const fullUrl = url.startsWith('http://') || url.startsWith('https://')
    ? url
    : `${BASE_URL}${url.startsWith('/') ? '' : '/'}${url}`;

  console.log(`Making API request to: ${fullUrl}`);
  console.log('Request headers:', headers);
  console.log('Request options:', options);

  const response = await fetch(fullUrl, {
    ...options,
    headers
  });

  console.log(`Response status: ${response.status} ${response.statusText}`);
  console.log('Response headers:', Object.fromEntries(response.headers.entries()));

  if (!response.ok) {
    // Handle error responses
    const errorText = await response.text();
    console.error(`Error response (${response.status}):`, errorText);

    let errorData;
    try {
      errorData = JSON.parse(errorText);
    } catch (e) {
      errorData = { detail: errorText };
    }

    // Handle 401 Unauthorized - automatically logout and redirect to login
    if (response.status === 401) {
      console.warn('401 Unauthorized detected - logging out user');
      removeAuthToken();
      localStorage.removeItem('iilm_user');

      // Dispatch custom event for global handling
      if (typeof window !== 'undefined') {
        window.dispatchEvent(new CustomEvent('unauthorized'));
        // Also redirect directly as a fallback
        setTimeout(() => {
          window.location.href = '/login';
        }, 100);
      }

      throw new Error('Your session has expired. Please log in again.');
    }

    throw new Error(errorData.detail || `Request failed with status ${response.status}`);
  }

  // Check if response has content
  const contentType = response.headers.get('content-type');
  const contentLength = response.headers.get('content-length');
  console.log('Response content type:', contentType);
  console.log('Response content length:', contentLength);

  // Handle empty responses (common for DELETE operations)
  if (contentLength === '0' || (!contentType && options.method === 'DELETE')) {
    console.log('Empty response detected (likely successful DELETE operation)');
    return null;
  }

  if (contentType?.includes('application/json')) {
    try {
      const jsonData = await response.json();
      console.log('Response JSON data:', jsonData);
      return jsonData;
    } catch (error) {
      console.warn('Failed to parse JSON response, treating as empty:', error);
      return null;
    }
  }

  const textData = await response.text();
  console.log('Response text data:', textData);
  return textData || null;
}
