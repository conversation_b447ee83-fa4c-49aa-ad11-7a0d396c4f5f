import { httpClient } from './httpClient';
import { CallData, mockCalls } from './mockData';

/**
 * Safely parses accuracy percentage from various formats
 * @param value The accuracy value from API (can be number, string, or null)
 * @returns Parsed number or null if invalid/string
 */
const parseAccuracyPercentage = (value: number | string | null | undefined): number | null => {
  if (value === null || value === undefined) {
    return null;
  }

  if (typeof value === 'number') {
    return isNaN(value) ? null : value;
  }

  if (typeof value === 'string') {
    // Handle string values that might be valid numbers
    const trimmedValue = value.trim().toLowerCase();

    // Check for explicit N/A values
    if (trimmedValue === 'na' || trimmedValue === 'n/a' || trimmedValue === 'not available' || trimmedValue === '') {
      return null;
    }

    // Try to parse as a number
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue)) {
      return numericValue;
    }

    // If it's not a valid number, treat as N/A
    return null;
  }

  return null;
};

// Define the interface for the API response
export interface CallDetailsResponse {
  id: string;
  call_id: string;
  call_duration: number;
  audio_url: string | null;
  processed: boolean;
  created_at: string;
  transcription_id: string | null;
  agent_name: string | null;
  call_date: string | null;
  call_time: string | null;
  group_name: string | null;
  student_id: string | null;
  student_name: string | null;
  call_direction: string | null;
  call_source: string | null;
  transcription: {
    id?: string;
    full_text?: string;
    text_preview?: string;
    duration_seconds?: number;
    created_at?: string;
    speaker_segments?: Array<{
      speaker: string;
      text: string;
    }>;
    speaker_count?: number;
    sentiment_info?: {
      overall_sentiment: 'positive' | 'neutral' | 'negative';
      sentiment_score: number;
      is_student_university_conversation?: boolean;
      conversation_topic?: string;
      summary?: string;
      key_points?: string[];
      student_sentiment?: string;
      assistant_sentiment?: string;
      speaker_roles?: Record<string, string>;
      action_items?: string[];
      relevant_category?: string;
    };
    accuracy_info?: {
      overall_assessment?: string;
      missed_information?: string[];
      incorrect_information?: Array<{
        provided: string;
        correct: string;
      }>;
      incomplete_information?: Array<{
        provided: string;
        complete: string;
      }>;
      correct_information?: string[];
      accuracy_percentage?: number | string | null;
      conversation_flags?: {
        satisfaction: boolean;
        confusion: boolean;
        urgency: boolean;
        frustration: boolean;
      };
      conversation_tone?: string;
      created_at?: string;
    };
    metadata?: {
      duration_seconds?: number;
      channels?: number;
    };
    segments?: Array<{
      speaker: string;
      text: string;
      start_time: number;
      end_time: number;
    }>;
  };
}

/**
 * Transforms the API response to a format that can be used by the UI
 * @param data The API response data
 * @returns Transformed call details data
 */
export const transformCallDetails = (data: CallDetailsResponse): CallData => {
  // Format the date and time - prioritize call_date and call_time
  let dateTime = '';
  if (data.call_date && data.call_time) {
    // Combine call_date and call_time into ISO format
    dateTime = `${data.call_date}T${data.call_time}`;
  } else if (data.created_at) {
    // Fallback to created_at if call_date/call_time not available
    const createdAt = new Date(data.created_at);
    dateTime = createdAt.toISOString();
  }

  // Format the duration (convert seconds to mm:ss format)
  const durationSeconds = data.call_duration || 0;
  const minutes = Math.floor(durationSeconds / 60);
  const seconds = durationSeconds % 60;
  const formattedDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

  // Extract sentiment information
  const sentiment = data.transcription?.sentiment_info?.overall_sentiment || 'neutral';
  const sentimentScore = data.transcription?.sentiment_info?.sentiment_score;

  // Extract flags from conversation_flags in accuracy_info
  let flags: Array<'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive'> = [];
  if (data.transcription?.accuracy_info?.conversation_flags) {
    const conversationFlags = data.transcription.accuracy_info.conversation_flags;

    // Handle both object and array formats
    if (typeof conversationFlags === 'object' && !Array.isArray(conversationFlags)) {
      // Convert object with boolean values to array of strings for true values
      flags = Object.entries(conversationFlags)
        .filter(([_, value]) => value === true)
        .map(([key, _]) => key) as Array<'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive'>;
    } else if (Array.isArray(conversationFlags)) {
      // If it's already an array, use it directly
      flags = conversationFlags as Array<'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive'>;
    }
  }

  // Filter and validate flags to ensure they match the expected types
  const validFlagTypes = ['frustration', 'confusion', 'urgency', 'satisfaction', 'abusive'];
  const validatedFlags = flags
    .filter(flag => typeof flag === 'string' && validFlagTypes.includes(flag.toLowerCase()))
    .map(flag => flag.toLowerCase()) as Array<'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive'>;

  // Extract accuracy information using the safe parser
  const infoAccuracy = parseAccuracyPercentage(data.transcription?.accuracy_info?.accuracy_percentage);

  // Extract transcript from full_text or build from speaker_segments if available
  let transcript = data.transcription?.full_text || '';
  if (!transcript && data.transcription?.speaker_segments && data.transcription.speaker_segments.length > 0) {
    transcript = data.transcription.speaker_segments.map(segment =>
      `${segment.speaker}: ${segment.text}`
    ).join('\n\n');
  }

  // Extract missed information
  const missedInfo = data.transcription?.accuracy_info?.missed_information || [];

  // Extract incomplete information
  const incompleteInfo = data.transcription?.accuracy_info?.incomplete_information || [];

  // Extract segments from speaker_segments or segments
  const segments = data.transcription?.speaker_segments?.map(segment => ({
    speaker: segment.speaker,
    text: segment.text,
    // Add dummy start/end times if not available
    start_time: 0,
    end_time: 0
  })) || data.transcription?.segments || [];

  // Extract sentiment summary
  const sentimentSummary = data.transcription?.sentiment_info?.summary || '';

  // Extract conversation topic
  const conversationTopic = data.transcription?.sentiment_info?.conversation_topic || '';

  // Extract relevant category
  const relevantCategory = data.transcription?.sentiment_info?.relevant_category || '';

  // Extract conversation tone
  const conversationTone = data.transcription?.accuracy_info?.conversation_tone || '';

  // Extract key points
  const keyPoints = data.transcription?.sentiment_info?.key_points || [];

  // Extract speaker roles
  const speakerRoles = data.transcription?.sentiment_info?.speaker_roles || {};

  // Extract action items
  const actionItems = data.transcription?.sentiment_info?.action_items || [];

  // Extract conversation flags
  const conversationFlags = data.transcription?.accuracy_info?.conversation_flags || {
    satisfaction: false,
    confusion: false,
    urgency: false,
    frustration: false,
    abusive: false
  };

  return {
    id: data.id || data.call_id || '',
    dateTime,
    counselor: data.agent_name || 'N/A',
    studentNumber: data.student_name || data.student_id || 'N/A',
    duration: formattedDuration,
    sentiment: sentiment as 'positive' | 'neutral' | 'negative',
    flags: validatedFlags,
    infoAccuracy,
    transcript,
    summary: sentimentSummary || '',
    missedInfo,
    incompleteInfo,
    audioUrl: data.audio_url || '',
    transcriptionId: data.transcription_id || '',
    callDirection: data.call_direction || '',
    callSource: data.call_source || '',
    groupName: data.group_name || '',
    sentimentScore,
    segments,
    incorrectInfo: data.transcription?.accuracy_info?.incorrect_information || [],
    // Additional sentiment info
    conversationTopic,
    relevantCategory,
    conversationTone,
    isStudentUniversityConversation: data.transcription?.sentiment_info?.is_student_university_conversation || false,
    sentimentSummary,
    keyPoints,
    studentSentiment: data.transcription?.sentiment_info?.student_sentiment || '',
    assistantSentiment: data.transcription?.sentiment_info?.assistant_sentiment || '',
    speakerRoles,
    actionItems,
    conversationFlags
  };
};

/**
 * Fetches call details by ID from the API
 * @param callId The ID of the call to fetch
 * @returns Promise with call details data
 */
export const fetchCallDetails = async (callId: string): Promise<CallData> => {
  try {
    console.log(`Fetching call details for ID: ${callId}`);
    const response = await httpClient.get(`/api/v1/transcription/calls/${callId}`);
    console.log('Call details API response:', response);

    if (!response) {
      console.error('Empty API response');
      throw new Error('Failed to fetch call details');
    }

    return transformCallDetails(response as CallDetailsResponse);
  } catch (error) {
    console.error(`Error fetching call details for ID ${callId}:`, error);

    // For development, fall back to mock data if available
    const mockCall = mockCalls.find(call => call.id === callId);
    if (mockCall) {
      console.warn('Using mock data for call details');
      return mockCall;
    }

    throw new Error(error instanceof Error ? error.message : 'Failed to fetch call details');
  }
};
