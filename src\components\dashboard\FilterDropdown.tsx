import { useState } from 'react';
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator
} from "@/components/ui/dropdown-menu";
import { Filter } from 'lucide-react';

// Split into two separate types
export type FlagFilterType = "frustration" | "confusion" | "urgency" | "satisfaction";
export type SentimentFilterType = "positive" | "neutral" | "negative";
export type FilterType = FlagFilterType | SentimentFilterType;

interface FilterDropdownProps {
  onFilterChange: (filter: FilterType) => void;
  selectedFilters: FilterType[];
}

const FilterDropdown = ({ onFilterChange, selectedFilters }: FilterDropdownProps) => {
  const flagOptions: { label: string, value: FlagFilterType }[] = [
    { label: "Frustration", value: "frustration" },
    { label: "Confusion", value: "confusion" },
    { label: "Urgency", value: "urgency" },
    { label: "Satisfaction", value: "satisfaction" }
  ];

  const sentimentOptions: { label: string, value: SentimentFilterType }[] = [
    { label: "Positive", value: "positive" },
    { label: "Neutral", value: "neutral" },
    { label: "Negative", value: "negative" }
  ];

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" className="gap-2">
          <Filter className="h-4 w-4" />
          Filter
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-56">
        <div className="px-2 py-1.5 text-sm font-semibold">Flag Types</div>
        {flagOptions.map((option) => (
          <DropdownMenuCheckboxItem
            key={option.value}
            checked={selectedFilters.includes(option.value)}
            onCheckedChange={() => onFilterChange(option.value)}
          >
            {option.label}
          </DropdownMenuCheckboxItem>
        ))}

        <DropdownMenuSeparator />

        <div className="px-2 py-1.5 text-sm font-semibold">Sentiment</div>
        {sentimentOptions.map((option) => (
          <DropdownMenuCheckboxItem
            key={option.value}
            checked={selectedFilters.includes(option.value)}
            onCheckedChange={() => onFilterChange(option.value)}
          >
            {option.label}
          </DropdownMenuCheckboxItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default FilterDropdown;
