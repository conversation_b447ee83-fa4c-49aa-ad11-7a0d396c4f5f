import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Loader, Plus, Edit, Trash2, ArrowLeft, Save, X } from 'lucide-react';
import { useToast } from '@/components/ui/use-toast';
import { errorToast } from '@/components/ui/custom-toast';
import {
  fetchSchoolInfoCategories,
  createOrUpdateSchoolInfo,
  updateSchoolInfoCategory,
  deleteSchoolInfoCategory,
  SchoolInfoCategory,
  SchoolInfoRequest
} from '@/services/universityInfoService';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';

const UniversityInfoManagement = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [categories, setCategories] = useState<SchoolInfoCategory[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showAddForm, setShowAddForm] = useState(false);
  const [editingCategory, setEditingCategory] = useState<SchoolInfoCategory | null>(null);
  const [deleteConfirmCategory, setDeleteConfirmCategory] = useState<SchoolInfoCategory | null>(null);
  const [deletingCategoryId, setDeletingCategoryId] = useState<string | null>(null);

  // Form state
  const [formData, setFormData] = useState<SchoolInfoRequest>({
    category: '',
    content: ''
  });

  // Load categories on component mount and scroll to top
  useEffect(() => {
    window.scrollTo(0, 0);
    loadCategories();
  }, []);

  const loadCategories = async () => {
    try {
      setIsLoading(true);
      const data = await fetchSchoolInfoCategories();
      setCategories(data);
    } catch (error) {
      console.error('Failed to load categories:', error);
      errorToast(
        'Error',
        'Failed to load university information. Please try again later.'
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCategory = async () => {
    if (!formData.category.trim() || !formData.content.trim()) {
      errorToast('Validation Error', 'Please fill in both category name and content.');
      return;
    }

    try {
      setIsSubmitting(true);
      await createOrUpdateSchoolInfo(formData);

      toast({
        title: "Success",
        description: "University information category added successfully.",
      });

      // Reset form and reload data
      setFormData({ category: '', content: '' });
      setShowAddForm(false);
      await loadCategories();
    } catch (error) {
      console.error('Failed to add category:', error);
      errorToast(
        'Error',
        error instanceof Error ? error.message : 'Failed to add category. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleEditCategory = async () => {
    if (!editingCategory || !formData.content.trim()) {
      errorToast('Validation Error', 'Please provide content for the category.');
      return;
    }

    try {
      setIsSubmitting(true);
      await updateSchoolInfoCategory(editingCategory.category, formData.content);

      toast({
        title: "Success",
        description: "University information category updated successfully.",
      });

      // Reset form and reload data
      setFormData({ category: '', content: '' });
      setEditingCategory(null);
      await loadCategories();
    } catch (error) {
      console.error('Failed to update category:', error);
      errorToast(
        'Error',
        error instanceof Error ? error.message : 'Failed to update category. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleDeleteCategory = async () => {
    if (!deleteConfirmCategory) return;

    try {
      setDeletingCategoryId(deleteConfirmCategory.id);
      setIsSubmitting(true);

      // Use the category name for deletion as per API specification
      await deleteSchoolInfoCategory(deleteConfirmCategory.category);

      toast({
        title: "Success",
        description: "University information category deleted successfully.",
      });

      setDeleteConfirmCategory(null);
      await loadCategories();
    } catch (error) {
      console.error('Failed to delete category:', error);
      errorToast(
        'Error',
        error instanceof Error ? error.message : 'Failed to delete category. Please try again.'
      );
    } finally {
      setIsSubmitting(false);
      setDeletingCategoryId(null);
    }
  };

  const startEdit = (category: SchoolInfoCategory) => {
    setEditingCategory(category);
    setFormData({
      category: category.category,
      content: category.content
    });
    setShowAddForm(false);
  };

  const cancelEdit = () => {
    setEditingCategory(null);
    setFormData({ category: '', content: '' });
  };

  const startAdd = () => {
    setShowAddForm(true);
    setEditingCategory(null);
    setFormData({ category: '', content: '' });
  };

  const cancelAdd = () => {
    setShowAddForm(false);
    setFormData({ category: '', content: '' });
  };

  // Truncate content for display
  const truncateContent = (content: string, maxLength: number = 200) => {
    if (content.length <= maxLength) return content;
    return content.substring(0, maxLength) + '...';
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
        <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[60vh]">
          <div className="text-center">
            <Loader className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
            <p className="text-lg text-gray-600">Loading university information...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8 animate-fade-in">
        {/* Header Section with Beautiful Gradient */}
        <div className="bg-gradient-primary rounded-2xl p-6 mb-8 shadow-beautiful text-white">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">University Information Management</h1>
              <p className="text-blue-100">Manage and organize university information categories</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="secondary"
                onClick={() => navigate('/')}
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm shadow-lg flex items-center gap-2"
              >
                <ArrowLeft className="h-4 w-4" />
                Back to Dashboard
              </Button>
              <Button
                onClick={startAdd}
                variant="secondary"
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm shadow-lg flex items-center gap-2"
                disabled={showAddForm || editingCategory !== null || deletingCategoryId !== null}
              >
                <Plus className="h-4 w-4" />
                Add Category
              </Button>
            </div>
          </div>
        </div>

        {/* Add Category Form */}
        {showAddForm && (
          <Card className="bg-white rounded-2xl shadow-card border border-gray-100 mb-8 animate-scale-in">
            <CardHeader>
              <CardTitle className="text-xl font-semibold text-gray-800">Add New Category</CardTitle>
              <CardDescription className="text-gray-600">
                Create a new university information category. Content can be plain text or a Google Drive URL.
              </CardDescription>
            </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <Label htmlFor="category">Category Name</Label>
              <Input
                id="category"
                value={formData.category}
                onChange={(e) => setFormData({ ...formData, category: e.target.value })}
                placeholder="e.g., admission, courses, fees"
                disabled={isSubmitting}
              />
            </div>
            <div>
              <Label htmlFor="content">Content</Label>
              <Textarea
                id="content"
                value={formData.content}
                onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                placeholder="Enter detailed information or Google Drive URL..."
                rows={8}
                disabled={isSubmitting}
              />
            </div>
            <div className="flex gap-3">
              <Button
                onClick={handleAddCategory}
                disabled={isSubmitting}
                className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg"
              >
                {isSubmitting ? <Loader className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
                Save Category
              </Button>
              <Button
                variant="outline"
                onClick={cancelAdd}
                disabled={isSubmitting}
                className="border-gray-300 hover:bg-gray-50"
              >
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
            </div>
          </CardContent>
        </Card>
      )}



        {/* Categories List */}
        <div className="space-y-6">
          {categories.length === 0 ? (
            <Card className="bg-white rounded-2xl shadow-card border border-gray-100">
              <CardContent className="py-12 text-center">
                <div className="w-16 h-16 bg-gray-200 rounded-full flex items-center justify-center mx-auto mb-4">
                  <div className="w-8 h-8 bg-gray-400 rounded-full"></div>
                </div>
                <p className="text-gray-500 text-lg font-medium">No university information categories found.</p>
                <p className="text-sm text-gray-400 mt-2">Click "Add Category" to create your first category.</p>
              </CardContent>
            </Card>
          ) : (
            categories.map((category) => (
              <div key={category.id}>
                <Card className="bg-white rounded-2xl shadow-card hover:shadow-beautiful transition-all duration-300 border border-gray-100 animate-fade-in">
                  <CardHeader>
                    <div className="flex justify-between items-start">
                      <div>
                        <CardTitle className="capitalize text-xl font-semibold text-gray-800">{category.category}</CardTitle>
                        <CardDescription className="text-gray-600">
                          Created: {new Date(category.created_at).toLocaleDateString()} |
                          Updated: {new Date(category.updated_at).toLocaleDateString()}
                        </CardDescription>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => startEdit(category)}
                          disabled={showAddForm || (editingCategory !== null && editingCategory.id !== category.id) || deletingCategoryId === category.id}
                          className="border-blue-300 text-blue-600 hover:bg-blue-50"
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => setDeleteConfirmCategory(category)}
                          disabled={showAddForm || editingCategory !== null || deletingCategoryId === category.id}
                          className="border-red-300 text-red-600 hover:bg-red-50"
                        >
                          {deletingCategoryId === category.id ? (
                            <Loader className="h-4 w-4 animate-spin" />
                          ) : (
                            <Trash2 className="h-4 w-4" />
                          )}
                        </Button>
                      </div>
                  </div>
                </CardHeader>
                <CardContent>
                  {editingCategory && editingCategory.id === category.id ? (
                    // Inline edit form
                    <div className="space-y-4">
                      <div>
                        <Label htmlFor={`edit-content-${category.id}`}>Content</Label>
                        <Textarea
                          id={`edit-content-${category.id}`}
                          value={formData.content}
                          onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                          placeholder="Enter detailed information or Google Drive URL..."
                          rows={8}
                          disabled={isSubmitting}
                        />
                      </div>
                        <div className="flex gap-3">
                          <Button
                            onClick={handleEditCategory}
                            disabled={isSubmitting}
                            className="bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 text-white shadow-lg"
                          >
                            {isSubmitting ? <Loader className="h-4 w-4 animate-spin mr-2" /> : <Save className="h-4 w-4 mr-2" />}
                            Update Category
                          </Button>
                          <Button
                            variant="outline"
                            onClick={cancelEdit}
                            disabled={isSubmitting}
                            className="border-gray-300 hover:bg-gray-50"
                          >
                            <X className="h-4 w-4 mr-2" />
                            Cancel
                          </Button>
                        </div>
                    </div>
                  ) : (
                    // Display content
                    <div>
                      <div className="whitespace-pre-wrap text-sm text-gray-700">
                        {truncateContent(category.content)}
                      </div>
                      {category.content.length > 200 && (
                        <p className="text-xs text-gray-500 mt-2">
                          Content truncated. Click edit to view full content.
                        </p>
                      )}
                    </div>
                  )}
                </CardContent>
              </Card>
            </div>
          ))
          )}
        </div>

        {/* Delete Confirmation Dialog */}
      <Dialog open={!!deleteConfirmCategory} onOpenChange={(open) => {
        if (!open && !isSubmitting) {
          setDeleteConfirmCategory(null);
        }
      }}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete the "{deleteConfirmCategory?.category}" category?
              This action cannot be undone and will permanently remove all content associated with this category.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteConfirmCategory(null)}
              disabled={isSubmitting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCategory}
              disabled={isSubmitting}
            >
              {isSubmitting ? <Loader className="h-4 w-4 animate-spin mr-2" /> : <Trash2 className="h-4 w-4 mr-2" />}
              Delete Category
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
      </div>
    </div>
  );
};

export default UniversityInfoManagement;
