import React, { useState } from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { cn } from '@/lib/utils';
import { DateFilters } from '@/services/dashboardService';

interface DateFilterPanelProps {
  dateFilters: DateFilters;
  onDateFiltersChange: (filters: DateFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
  showAnalyticsLabel?: boolean;
}

const DateFilterPanel: React.FC<DateFilterPanelProps> = ({
  dateFilters,
  onDateFiltersChange,
  onClearFilters,
  isLoading = false,
  showAnalyticsLabel = true
}) => {
  const [specificDate, setSpecificDate] = useState<Date | undefined>(
    dateFilters.specific_date ? new Date(dateFilters.specific_date) : undefined
  );
  const [startDate, setStartDate] = useState<Date | undefined>(
    dateFilters.start_date ? new Date(dateFilters.start_date) : undefined
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    dateFilters.end_date ? new Date(dateFilters.end_date) : undefined
  );

  const handleFilterTypeChange = (filterType: DateFilters['filter_type']) => {
    const newFilters: DateFilters = { filter_type: filterType };

    // Clear date fields when changing filter type
    setSpecificDate(undefined);
    setStartDate(undefined);
    setEndDate(undefined);

    // Set today's date for today filter
    if (filterType === 'today') {
      const today = format(new Date(), 'yyyy-MM-dd');
      newFilters.specific_date = today;
    }
    // Set yesterday's date for yesterday filter
    else if (filterType === 'yesterday') {
      const yesterday = new Date();
      yesterday.setDate(yesterday.getDate() - 1);
      newFilters.specific_date = format(yesterday, 'yyyy-MM-dd');
    }

    onDateFiltersChange(newFilters);
  };

  const handleSpecificDateChange = (date: Date | undefined) => {
    setSpecificDate(date);
    if (date) {
      onDateFiltersChange({
        ...dateFilters,
        specific_date: format(date, 'yyyy-MM-dd')
      });
    }
  };

  const handleStartDateChange = (date: Date | undefined) => {
    setStartDate(date);
    if (date) {
      const newFilters = {
        ...dateFilters,
        start_date: format(date, 'yyyy-MM-dd')
      };
      // Clear end_date if it's before start_date
      if (endDate && date > endDate) {
        setEndDate(undefined);
        delete newFilters.end_date;
      }
      onDateFiltersChange(newFilters);
    }
  };

  const handleEndDateChange = (date: Date | undefined) => {
    setEndDate(date);
    if (date && startDate) {
      onDateFiltersChange({
        ...dateFilters,
        end_date: format(date, 'yyyy-MM-dd')
      });
    }
  };

  const getActiveFilterCount = () => {
    let count = 0;
    if (dateFilters.filter_type !== 'all') count++;
    return count;
  };

  const getFilterDescription = () => {
    switch (dateFilters.filter_type) {
      case 'today':
        return 'Today';
      case 'yesterday':
        return 'Yesterday';
      case 'specific_day':
        return dateFilters.specific_date ? format(new Date(dateFilters.specific_date), 'MMM dd, yyyy') : 'Specific Day';
      case 'date_range':
        if (dateFilters.start_date && dateFilters.end_date) {
          return `${format(new Date(dateFilters.start_date), 'MMM dd')} - ${format(new Date(dateFilters.end_date), 'MMM dd, yyyy')}`;
        }
        return 'Date Range';
      default:
        return 'All Time';
    }
  };

  const isValidConfiguration = () => {
    switch (dateFilters.filter_type) {
      case 'specific_day':
        return !!dateFilters.specific_date;
      case 'date_range':
        return !!dateFilters.start_date && !!dateFilters.end_date;
      default:
        return true;
    }
  };

  return (
    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-blue-100/50 shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold text-blue-800 flex items-center gap-2">
            <CalendarIcon className="h-4 w-4" />
            Date Filters
            {showAnalyticsLabel && (
              <Badge variant="outline" className="text-xs bg-blue-100 text-blue-700 border-blue-300">
                Analytics + Calls
              </Badge>
            )}
          </CardTitle>
          {getActiveFilterCount() > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="h-7 px-2 text-xs text-blue-600 hover:text-blue-800 hover:bg-blue-100"
              disabled={isLoading}
            >
              <X className="h-3 w-3 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-600">Filter Type</label>
          <Select
            value={dateFilters.filter_type}
            onValueChange={handleFilterTypeChange}
            disabled={isLoading}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select date filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Time</SelectItem>
              <SelectItem value="today">Today</SelectItem>
              <SelectItem value="yesterday">Yesterday</SelectItem>
              <SelectItem value="specific_day">Specific Day</SelectItem>
              <SelectItem value="date_range">Date Range</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {dateFilters.filter_type === 'specific_day' && (
          <div className="space-y-2">
            <label className="text-xs font-medium text-gray-600">Select Date</label>
            <Popover>
              <PopoverTrigger asChild>
                <Button
                  variant="outline"
                  className={cn(
                    "w-full justify-start text-left font-normal",
                    !specificDate && "text-muted-foreground"
                  )}
                  disabled={isLoading}
                >
                  <CalendarIcon className="mr-2 h-4 w-4" />
                  {specificDate ? format(specificDate, "PPP") : "Pick a date"}
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-auto p-0" align="start">
                <Calendar
                  mode="single"
                  selected={specificDate}
                  onSelect={handleSpecificDateChange}
                  initialFocus
                />
              </PopoverContent>
            </Popover>
          </div>
        )}

        {dateFilters.filter_type === 'date_range' && (
          <div className="space-y-3">
            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-600">Start Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !startDate && "text-muted-foreground"
                    )}
                    disabled={isLoading}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {startDate ? format(startDate, "PPP") : "Pick start date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={startDate}
                    onSelect={handleStartDateChange}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <label className="text-xs font-medium text-gray-600">End Date</label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !endDate && "text-muted-foreground"
                    )}
                    disabled={isLoading || !startDate}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {endDate ? format(endDate, "PPP") : "Pick end date"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={endDate}
                    onSelect={handleEndDateChange}
                    disabled={(date) => startDate ? date < startDate : false}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
            </div>
          </div>
        )}

        {dateFilters.filter_type !== 'all' && (
          <div className="pt-2 border-t border-blue-200">
            <div className="text-xs text-gray-600 mb-1">Active Filter:</div>
            <Badge
              variant={isValidConfiguration() ? "default" : "destructive"}
              className="text-xs"
            >
              {getFilterDescription()}
            </Badge>
            {!isValidConfiguration() && (
              <div className="text-xs text-red-600 mt-1">
                Please complete the filter configuration
              </div>
            )}
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default DateFilterPanel;
