import React, { useState, useCallback } from 'react';
import { X, Filter, User } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Label } from '@/components/ui/label';
import { CallSpecificFilters } from '@/services/dashboardService';
import { useDebounce } from '@/hooks/useDebounce';

interface CallFilterPanelProps {
  callFilters: CallSpecificFilters;
  onCallFiltersChange: (filters: CallSpecificFilters) => void;
  onClearFilters: () => void;
  isLoading?: boolean;
}

const CallFilterPanel: React.FC<CallFilterPanelProps> = ({
  callFilters,
  onCallFiltersChange,
  onClearFilters,
  isLoading = false
}) => {
  const [localAgentName, setLocalAgentName] = useState(callFilters.agent_name || '');

  // Debounce text inputs to avoid excessive API calls
  const debouncedAgentName = useDebounce(localAgentName, 500);

  // Update filters when debounced values change
  React.useEffect(() => {
    if (debouncedAgentName !== callFilters.agent_name) {
      onCallFiltersChange({
        ...callFilters,
        agent_name: debouncedAgentName || undefined
      });
    }
  }, [debouncedAgentName, callFilters, onCallFiltersChange]);

  const sentimentOptions = [
    { label: 'Positive', value: 'positive' as const },
    { label: 'Neutral', value: 'neutral' as const },
    { label: 'Negative', value: 'negative' as const }
  ];

  const flagOptions = [
    { label: 'Frustration', value: 'frustration' as const },
    { label: 'Confusion', value: 'confusion' as const },
    { label: 'Urgency', value: 'urgency' as const },
    { label: 'Satisfaction', value: 'satisfaction' as const },
    { label: 'Abusive', value: 'abusive' as const }
  ];

  const handleSentimentChange = useCallback((sentiment: 'positive' | 'neutral' | 'negative') => {
    onCallFiltersChange({
      ...callFilters,
      sentiment_filter: sentiment
    });
  }, [callFilters, onCallFiltersChange]);

  const handleFlagChange = useCallback((flag: 'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive') => {
    onCallFiltersChange({
      ...callFilters,
      conversation_flags: flag
    });
  }, [callFilters, onCallFiltersChange]);

  const handleClearSentiment = useCallback(() => {
    onCallFiltersChange({
      ...callFilters,
      sentiment_filter: undefined
    });
  }, [callFilters, onCallFiltersChange]);

  const handleClearFlags = useCallback(() => {
    onCallFiltersChange({
      ...callFilters,
      conversation_flags: undefined
    });
  }, [callFilters, onCallFiltersChange]);

  const getActiveFilterCount = () => {
    let count = 0;
    if (callFilters.sentiment_filter) count++;
    if (callFilters.conversation_flags) count++;
    if (callFilters.agent_name && callFilters.agent_name.trim()) count++;
    return count;
  };

  const clearAgentName = () => {
    setLocalAgentName('');
    onCallFiltersChange({
      ...callFilters,
      agent_name: undefined
    });
  };

  return (
    <Card className="border-gray-200 bg-gradient-to-br from-gray-50 to-gray-100/50 shadow-sm">
      <CardHeader className="pb-3">
        <div className="flex items-center justify-between">
          <CardTitle className="text-sm font-semibold text-gray-800 flex items-center gap-2">
            <Filter className="h-4 w-4" />
            Call Filters
            <Badge variant="outline" className="text-xs bg-gray-100 text-gray-700 border-gray-300">
              Calls Only
            </Badge>
          </CardTitle>
          {getActiveFilterCount() > 0 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onClearFilters}
              className="h-7 px-2 text-xs text-gray-600 hover:text-gray-800 hover:bg-gray-100"
              disabled={isLoading}
            >
              <X className="h-3 w-3 mr-1" />
              Clear
            </Button>
          )}
        </div>
      </CardHeader>
      <CardContent className="space-y-3">

        {/* Counselor Name Input */}
        <div className="space-y-2">
          <label className="text-xs font-medium text-gray-600">Counselor Name</label>
          <div className="relative">
            <User className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
            <Input
              placeholder="Filter by counselor name..."
              value={localAgentName}
              onChange={(e) => setLocalAgentName(e.target.value)}
              className="pl-10 pr-8"
              disabled={isLoading}
            />
            {localAgentName && (
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAgentName}
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0 text-gray-400 hover:text-gray-600"
              >
                <X className="h-3 w-3" />
              </Button>
            )}
          </div>
        </div>

        {/* Sentiment Filter */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-xs font-medium text-gray-600">Sentiment</label>
            {callFilters.sentiment_filter && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearSentiment}
                className="h-5 px-2 text-xs text-gray-500 hover:text-gray-700"
              >
                Clear
              </Button>
            )}
          </div>
          <RadioGroup
            value={callFilters.sentiment_filter || ''}
            onValueChange={handleSentimentChange}
            disabled={isLoading}
            className="flex flex-wrap gap-3"
          >
            {sentimentOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-1.5">
                <RadioGroupItem value={option.value} id={`sentiment-${option.value}`} className="h-3 w-3" />
                <Label
                  htmlFor={`sentiment-${option.value}`}
                  className="text-xs font-normal cursor-pointer text-gray-700"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Conversation Flags Filter */}
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <label className="text-xs font-medium text-gray-600">Conversation Flags</label>
            {callFilters.conversation_flags && (
              <Button
                variant="ghost"
                size="sm"
                onClick={handleClearFlags}
                className="h-5 px-2 text-xs text-gray-500 hover:text-gray-700"
              >
                Clear
              </Button>
            )}
          </div>
          <RadioGroup
            value={callFilters.conversation_flags || ''}
            onValueChange={handleFlagChange}
            disabled={isLoading}
            className="grid grid-cols-2 sm:grid-cols-3 gap-2"
          >
            {flagOptions.map((option) => (
              <div key={option.value} className="flex items-center space-x-1.5">
                <RadioGroupItem value={option.value} id={`flag-${option.value}`} className="h-3 w-3" />
                <Label
                  htmlFor={`flag-${option.value}`}
                  className="text-xs font-normal cursor-pointer text-gray-700"
                >
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </div>

        {/* Active Filters Display */}
        {getActiveFilterCount() > 0 && (
          <div className="pt-3 border-t border-gray-200">
            <div className="text-xs text-gray-600 mb-2">Active Filters:</div>
            <div className="flex flex-wrap gap-1">
              {callFilters.sentiment_filter && (
                <Badge variant="outline" className="text-xs">
                  {callFilters.sentiment_filter}
                </Badge>
              )}
              {callFilters.conversation_flags && (
                <Badge variant="outline" className="text-xs">
                  {callFilters.conversation_flags}
                </Badge>
              )}
              {callFilters.agent_name && (
                <Badge variant="outline" className="text-xs">
                  Counselor: {callFilters.agent_name}
                </Badge>
              )}
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
};

export default CallFilterPanel;
