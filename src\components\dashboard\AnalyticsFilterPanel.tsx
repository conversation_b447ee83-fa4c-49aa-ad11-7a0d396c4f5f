import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, RotateCcw, Check, RefreshCw, BarChart3 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import DateFilterPanel from './DateFilterPanel';
import { DateFilters } from '@/services/dashboardService';
import { cn } from '@/lib/utils';

interface AnalyticsFilterPanelProps {
  dateFilters: DateFilters;
  onApplyFilters: (dateFilters: DateFilters) => void;
  onClearAllFilters: () => void;
  isLoading?: boolean;
  className?: string;
}

const AnalyticsFilterPanel: React.FC<AnalyticsFilterPanelProps> = ({
  dateFilters,
  onApplyFilters,
  onClearAllFilters,
  isLoading = false,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Local state for pending filter changes
  const [localDateFilters, setLocalDateFilters] = useState<DateFilters>(dateFilters);

  // Track if there are pending changes
  const [hasUnappliedChanges, setHasUnappliedChanges] = useState(false);

  // Update local state when props change (e.g., when filters are applied or cleared externally)
  useEffect(() => {
    setLocalDateFilters(dateFilters);
    setHasUnappliedChanges(false);
  }, [dateFilters]);

  // Check if local filters differ from applied filters
  useEffect(() => {
    const dateChanged = JSON.stringify(localDateFilters) !== JSON.stringify(dateFilters);
    setHasUnappliedChanges(dateChanged);
  }, [localDateFilters, dateFilters]);

  const getTotalActiveFilters = () => {
    let count = 0;
    // Use applied filters for count (not local)
    if (dateFilters.filter_type !== 'all') count++;
    return count;
  };

  const getTotalLocalFilters = () => {
    let count = 0;
    // Use local filters for pending count
    if (localDateFilters.filter_type !== 'all') count++;
    return count;
  };

  const handleApplyFilters = () => {
    onApplyFilters(localDateFilters);
  };

  const handleResetFilters = () => {
    setLocalDateFilters({ filter_type: 'all' });
  };

  const handleClearDateFilters = () => {
    setLocalDateFilters({ filter_type: 'all' });
  };

  const getFilterSummary = () => {
    const summary = [];

    // Date filter summary (use applied filters)
    if (dateFilters.filter_type !== 'all') {
      switch (dateFilters.filter_type) {
        case 'today':
          summary.push('Today');
          break;
        case 'yesterday':
          summary.push('Yesterday');
          break;
        case 'specific_day':
          summary.push('Specific Day');
          break;
        case 'date_range':
          summary.push('Date Range');
          break;
      }
    }

    return summary;
  };

  const totalFilters = getTotalActiveFilters();
  const totalLocalFilters = getTotalLocalFilters();
  const filterSummary = getFilterSummary();

  return (
    <div className={className}>
      <Card className="border border-blue-200 shadow-sm hover:shadow-md transition-all duration-200 bg-gradient-to-br from-blue-50 to-blue-100/30">
        <CardHeader
          className="cursor-pointer hover:bg-gradient-to-r hover:from-blue-100 hover:to-blue-50 transition-all duration-200 pb-4"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <div className="flex items-center gap-3">
              <CardTitle className="text-lg font-semibold text-blue-800 flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-blue-600" />
                Analytics Filters
              </CardTitle>
              <div className="flex items-center gap-2">
                {totalFilters > 0 && (
                  <Badge variant="default" className="bg-blue-600 text-white text-xs px-2 py-1">
                    {totalFilters} active
                  </Badge>
                )}
                {hasUnappliedChanges && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs px-2 py-1 animate-pulse">
                    {totalLocalFilters} pending
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between gap-3">
              {totalFilters > 0 && !isExpanded && (
                <div className="flex flex-wrap gap-1 max-w-xs sm:max-w-md">
                  {filterSummary.slice(0, 2).map((filter, index) => (
                    <Badge key={index} variant="outline" className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">
                      {filter}
                    </Badge>
                  ))}
                  {filterSummary.length > 2 && (
                    <Badge variant="outline" className="text-xs px-2 py-1 bg-gray-50 text-gray-600">
                      +{filterSummary.length - 2}
                    </Badge>
                  )}
                </div>
              )}
              <div className="flex items-center gap-2">
                {!isExpanded && hasUnappliedChanges && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleApplyFilters();
                    }}
                    className="h-7 px-3 text-xs bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100"
                  >
                    Apply Now
                  </Button>
                )}
                {isExpanded ? (
                  <ChevronUp className="h-5 w-5 text-blue-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-blue-500" />
                )}
              </div>
            </div>
          </div>

          {!isExpanded && (
            <div className="text-xs text-blue-600 mt-2 flex items-center gap-2">
              {hasUnappliedChanges ? (
                <span className="text-orange-600 font-medium">
                  ⚠️ You have pending changes
                </span>
              ) : totalFilters > 0 ? (
                <span>Click to modify active filters</span>
              ) : (
                <span>Click to add filters for analytics data</span>
              )}
            </div>
          )}
        </CardHeader>

        {isExpanded && (
          <CardContent className="pt-0 pb-6">
              <div className="space-y-5">
                {/* Apply/Reset Buttons */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 p-4 bg-gradient-to-r from-blue-100 to-blue-50 rounded-lg border border-blue-200">
                  <div className="flex items-center gap-2">
                    {hasUnappliedChanges ? (
                      <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs">
                        ⚠️ Pending changes
                      </Badge>
                    ) : (
                      <span className="text-sm text-blue-700">Configure date filters for analytics</span>
                    )}
                  </div>
                  <div className="flex gap-2 w-full sm:w-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResetFilters}
                      className="flex-1 sm:flex-none text-blue-600 border-blue-200 hover:bg-blue-50 h-8"
                      disabled={isLoading || totalLocalFilters === 0}
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Reset
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleApplyFilters}
                      className={cn(
                        "flex-1 sm:flex-none bg-blue-600 text-white hover:bg-blue-700 h-8",
                        hasUnappliedChanges && "animate-pulse shadow-lg"
                      )}
                      disabled={isLoading}
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Apply Filters
                    </Button>
                  </div>
                </div>

                {/* Date Filters */}
                <DateFilterPanel
                  dateFilters={localDateFilters}
                  onDateFiltersChange={setLocalDateFilters}
                  onClearFilters={handleClearDateFilters}
                  isLoading={isLoading}
                  showAnalyticsLabel={false}
                />

                {/* Filter Impact Notice */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="text-xs text-blue-700 flex flex-col sm:flex-row sm:items-center gap-2">
                    <span className="font-medium text-blue-800">📊 Analytics Impact:</span>
                    <span>These filters affect dashboard metrics, charts, and analytics data</span>
                  </div>
                </div>
              </div>
            </CardContent>
        )}
      </Card>
    </div>
  );
};

export default AnalyticsFilterPanel;
