
import { getAuthToken } from './authService';
import { httpClient } from './httpClient';

interface ProcessCallsParams {
  start_date: string;
  end_date: string;
  max_concurrent?: number;
}

interface ManualTriggerRequest {
  date_override?: string; // YYYY-MM-DD format
  max_concurrent?: number; // Default will be 2
}

interface CronJobExecutionResponse {
  message: string;
  execution_id?: string;
  status: string;
}

export const processCalls = async (params: ProcessCallsParams): Promise<void> => {
  try {
    console.log('Processing calls with params:', params);

    // Make the API call but don't wait for the response
    fetch(`${import.meta.env.VITE_BASE_URL || 'http://localhost:8000'}/api/v1/transcription/process-calls?max_concurrent=${params.max_concurrent || 5}`, {
      method: 'POST',
      headers: {
        'accept': 'application/json',
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${getAuthToken()}`
      },
      body: JSON.stringify({
        start_date: params.start_date,
        end_date: params.end_date
      })
    }).then(response => {
      if (!response.ok) {
        console.error(`Process calls API returned error status: ${response.status}`);
      } else {
        console.log('Process calls API request successful');
      }
    }).catch(error => {
      console.error('Error in background process calls API request:', error);
    });

    // Return immediately without waiting for the response
    return;
  } catch (error) {
    console.error('Error initiating process calls:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to initiate call processing');
  }
};

// Helper to format date to the required format: "YYYY-MM-DD HH:MM:SS"
export const formatDateForApi = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');
  const hours = String(date.getHours()).padStart(2, '0');
  const minutes = String(date.getMinutes()).padStart(2, '0');
  const seconds = String(date.getSeconds()).padStart(2, '0');

  return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
};

// Helper to format date to YYYY-MM-DD format for the trigger API
export const formatDateForTrigger = (date: Date): string => {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, '0');
  const day = String(date.getDate()).padStart(2, '0');

  return `${year}-${month}-${day}`;
};

/**
 * Triggers the daily call processing job manually
 * @param params ManualTriggerRequest with optional date override and max concurrent
 * @returns Promise with the job execution response
 */
export const triggerDailyCallProcessing = async (params: ManualTriggerRequest = {}): Promise<CronJobExecutionResponse> => {
  try {
    console.log('Triggering daily call processing with params:', params);

    // Set default max_concurrent to 2 if not provided
    const requestData: ManualTriggerRequest = {
      max_concurrent: 2,
      ...params
    };

    const response = await httpClient.post('/api/v1/cron-jobs/trigger', requestData);
    console.log('Trigger daily call processing API response:', response);

    return response;
  } catch (error) {
    console.error('Failed to trigger daily call processing:', error);
    throw new Error('Failed to trigger call processing. Please try again later.');
  }
};
