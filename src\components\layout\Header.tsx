
import React from 'react';
import { Link } from 'react-router-dom';
import { Button } from "@/components/ui/button";
import { useToast } from "@/components/ui/use-toast";
import { LogOut, User } from "lucide-react";

interface HeaderProps {
  isLoggedIn: boolean;
  onLogout: () => void;
}

const Header: React.FC<HeaderProps> = ({ isLoggedIn, onLogout }) => {
  const { toast } = useToast();
  const username = localStorage.getItem('iilm_user');

  const handleLogout = () => {
    onLogout();
    toast({
      title: "Logged out successfully",
      description: "You have been logged out of your account",
    });
  };

  return (
    <header className="bg-iilm-primary shadow-sm border-b">
      <div className="container mx-auto px-4 py-3 flex justify-between items-center">
        <Link to="/" className="flex items-center gap-2">
          <img
            src="/lovable-uploads/daccb6d4-9aa1-4414-9df8-312566be8e76.png"
            alt="IILM University Logo"
            className="h-10"
          />
        </Link>

        {isLoggedIn && (
          <div className="flex items-center gap-4">
            <div className="flex items-center text-white">
              <User className="h-4 w-4 mr-1" />
              <span>{username || 'User'}</span>
            </div>
            <Button
              variant="ghost"
              size="sm"
              onClick={handleLogout}
              className="text-white hover:text-opacity-80"
            >
              <LogOut className="h-4 w-4 mr-2" />
              Logout
            </Button>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;
