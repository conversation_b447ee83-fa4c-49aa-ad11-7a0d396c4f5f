import React, { useState, useEffect } from 'react';
import { ChevronDown, ChevronUp, RotateCcw, Check, RefreshCw, Filter } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import DateFilterPanel from './DateFilterPanel';
import CallFilterPanel from './CallFilterPanel';
import { DateFilters, CallSpecificFilters } from '@/services/dashboardService';
import { cn } from '@/lib/utils';

interface ComprehensiveFilterPanelProps {
  dateFilters: DateFilters;
  callFilters: CallSpecificFilters;
  onApplyFilters: (dateFilters: DateFilters, callFilters: CallSpecificFilters) => void;
  onClearAllFilters: () => void;
  isLoading?: boolean;
  className?: string;
}

const ComprehensiveFilterPanel: React.FC<ComprehensiveFilterPanelProps> = ({
  dateFilters,
  callFilters,
  onApplyFilters,
  onClearAllFilters,
  isLoading = false,
  className = ''
}) => {
  const [isExpanded, setIsExpanded] = useState(false);

  // Local state for pending filter changes
  const [localDateFilters, setLocalDateFilters] = useState<DateFilters>(dateFilters);
  const [localCallFilters, setLocalCallFilters] = useState<CallSpecificFilters>(callFilters);

  // Track if there are pending changes
  const [hasUnappliedChanges, setHasUnappliedChanges] = useState(false);

  // Update local state when props change (e.g., when filters are applied or cleared externally)
  useEffect(() => {
    setLocalDateFilters(dateFilters);
    setLocalCallFilters(callFilters);
    setHasUnappliedChanges(false);
  }, [dateFilters, callFilters]);

  // Check if local filters differ from applied filters
  useEffect(() => {
    const dateChanged = JSON.stringify(localDateFilters) !== JSON.stringify(dateFilters);
    const callChanged = JSON.stringify(localCallFilters) !== JSON.stringify(callFilters);
    setHasUnappliedChanges(dateChanged || callChanged);
  }, [localDateFilters, localCallFilters, dateFilters, callFilters]);

  const getTotalActiveFilters = () => {
    let count = 0;

    // Use applied filters for count (not local)
    if (dateFilters.filter_type !== 'all') count++;

    if (callFilters.sentiment_filter) count++;
    if (callFilters.conversation_flags) count++;
    if (callFilters.agent_name && callFilters.agent_name.trim()) count++;

    return count;
  };

  const getTotalLocalFilters = () => {
    let count = 0;

    // Use local filters for pending count
    if (localDateFilters.filter_type !== 'all') count++;

    if (localCallFilters.sentiment_filter) count++;
    if (localCallFilters.conversation_flags) count++;
    if (localCallFilters.agent_name && localCallFilters.agent_name.trim()) count++;

    return count;
  };

  const handleApplyFilters = () => {
    onApplyFilters(localDateFilters, localCallFilters);
  };

  const handleResetFilters = () => {
    setLocalDateFilters({ filter_type: 'all' });
    setLocalCallFilters({});
  };

  const handleClearDateFilters = () => {
    setLocalDateFilters({ filter_type: 'all' });
  };

  const handleClearCallFilters = () => {
    setLocalCallFilters({});
  };

  const getFilterSummary = () => {
    const summary = [];

    // Date filter summary (use applied filters)
    if (dateFilters.filter_type !== 'all') {
      switch (dateFilters.filter_type) {
        case 'today':
          summary.push('Today');
          break;
        case 'yesterday':
          summary.push('Yesterday');
          break;
        case 'specific_day':
          summary.push('Specific Day');
          break;
        case 'date_range':
          summary.push('Date Range');
          break;
      }
    }

    // Call filter summary (use applied filters)
    if (callFilters.sentiment_filter) {
      summary.push(`Sentiment: ${callFilters.sentiment_filter}`);
    }

    if (callFilters.conversation_flags) {
      summary.push(`Flag: ${callFilters.conversation_flags}`);
    }

    if (callFilters.agent_name && callFilters.agent_name.trim()) {
      summary.push('Counselor Filter');
    }

    return summary;
  };

  const totalFilters = getTotalActiveFilters();
  const totalLocalFilters = getTotalLocalFilters();
  const filterSummary = getFilterSummary();

  return (
    <div className={className}>
      <Card className="border border-gray-200 shadow-sm hover:shadow-md transition-all duration-200">
        <CardHeader
          className="cursor-pointer hover:bg-gradient-to-r hover:from-blue-50 hover:to-gray-50 transition-all duration-200 pb-4"
          onClick={() => setIsExpanded(!isExpanded)}
        >
          <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-3">
            <div className="flex items-center gap-3">
              <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <Filter className="h-5 w-5 text-iilm-primary" />
                Filters
              </CardTitle>
              <div className="flex items-center gap-2">
                {totalFilters > 0 && (
                  <Badge variant="default" className="bg-iilm-primary text-white text-xs px-2 py-1">
                    {totalFilters} active
                  </Badge>
                )}
                {hasUnappliedChanges && (
                  <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs px-2 py-1 animate-pulse">
                    {totalLocalFilters} pending
                  </Badge>
                )}
              </div>
            </div>

            <div className="flex items-center justify-between gap-3">
              {totalFilters > 0 && !isExpanded && (
                <div className="flex flex-wrap gap-1 max-w-xs sm:max-w-md">
                  {filterSummary.slice(0, 2).map((filter, index) => (
                    <Badge key={index} variant="outline" className="text-xs px-2 py-1 bg-blue-50 text-blue-700 border-blue-200">
                      {filter}
                    </Badge>
                  ))}
                  {filterSummary.length > 2 && (
                    <Badge variant="outline" className="text-xs px-2 py-1 bg-gray-50 text-gray-600">
                      +{filterSummary.length - 2}
                    </Badge>
                  )}
                </div>
              )}
              <div className="flex items-center gap-2">
                {!isExpanded && hasUnappliedChanges && (
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      handleApplyFilters();
                    }}
                    className="h-7 px-3 text-xs bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100"
                  >
                    Apply Now
                  </Button>
                )}
                {isExpanded ? (
                  <ChevronUp className="h-5 w-5 text-gray-500" />
                ) : (
                  <ChevronDown className="h-5 w-5 text-gray-500" />
                )}
              </div>
            </div>
          </div>

          {!isExpanded && (
            <div className="text-xs text-gray-500 mt-2 flex items-center gap-2">
              {hasUnappliedChanges ? (
                <span className="text-orange-600 font-medium">
                  ⚠️ You have pending changes
                </span>
              ) : totalFilters > 0 ? (
                <span>Click to modify active filters</span>
              ) : (
                <span>Click to add filters</span>
              )}
            </div>
          )}
        </CardHeader>

        {isExpanded && (
          <CardContent className="pt-0 pb-6">
              <div className="space-y-5">
                {/* Apply/Reset Buttons */}
                <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3 p-4 bg-gradient-to-r from-blue-50 to-gray-50 rounded-lg border border-blue-100">
                  <div className="flex items-center gap-2">
                    {hasUnappliedChanges ? (
                      <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200 text-xs">
                        ⚠️ Pending changes
                      </Badge>
                    ) : (
                      <span className="text-sm text-gray-600">Configure your filters below</span>
                    )}
                  </div>
                  <div className="flex gap-2 w-full sm:w-auto">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleResetFilters}
                      className="flex-1 sm:flex-none text-gray-600 border-gray-200 hover:bg-gray-50 h-8"
                      disabled={isLoading || totalLocalFilters === 0}
                    >
                      <RefreshCw className="h-3 w-3 mr-1" />
                      Reset
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={handleApplyFilters}
                      className={cn(
                        "flex-1 sm:flex-none bg-iilm-primary text-white hover:bg-iilm-primary/90 h-8",
                        hasUnappliedChanges && "animate-pulse shadow-lg"
                      )}
                      disabled={isLoading}
                    >
                      <Check className="h-3 w-3 mr-1" />
                      Apply Filters
                    </Button>
                  </div>
                </div>

                {/* Filter Panels - Responsive Grid */}
                <div className="grid grid-cols-1 xl:grid-cols-2 gap-5">
                  {/* Date Filters */}
                  <DateFilterPanel
                    dateFilters={localDateFilters}
                    onDateFiltersChange={setLocalDateFilters}
                    onClearFilters={handleClearDateFilters}
                    isLoading={isLoading}
                  />

                  {/* Call-Specific Filters */}
                  <CallFilterPanel
                    callFilters={localCallFilters}
                    onCallFiltersChange={setLocalCallFilters}
                    onClearFilters={handleClearCallFilters}
                    isLoading={isLoading}
                  />
                </div>

                {/* Filter Impact Notice - Compact */}
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-3">
                  <div className="text-xs text-blue-700 flex flex-col sm:flex-row sm:items-center gap-2">
                    <span className="font-medium text-blue-800">💡 Quick tip:</span>
                    <span>Date filters affect analytics + calls • Call filters affect calls only • Click Apply to update data</span>
                  </div>
                </div>


              </div>
            </CardContent>
        )}
      </Card>
    </div>
  );
};

export default ComprehensiveFilterPanel;
