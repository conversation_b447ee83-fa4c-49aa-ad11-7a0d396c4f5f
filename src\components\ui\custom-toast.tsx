import React from 'react';
import { toast as hookToast } from '@/hooks/use-toast';
import { CheckCircle2, AlertCircle, Clock, X, Info } from 'lucide-react';

type ToastType = 'success' | 'error' | 'processing' | 'info';

interface CustomToastOptions {
  title: string;
  description?: string;
  duration?: number;
  className?: string;
  type?: ToastType;
}

/**
 * Custom toast notification with predefined styles and icons
 * @param options Toast options including title, description, and type
 */
export const customToast = (options: CustomToastOptions) => {
  const { title, description, duration = 6000, className = '', type = 'info' } = options;

  // Define icon and styles based on toast type
  let icon;
  let customClassName = '';

  switch (type) {
    case 'success':
      icon = <CheckCircle2 className="h-5 w-5" />;
      customClassName = 'bg-green-50 border-green-200 text-green-700';
      break;
    case 'error':
      icon = <AlertCircle className="h-5 w-5" />;
      customClassName = 'bg-red-50 border-red-200 text-red-700';
      break;
    case 'processing':
      icon = <Clock className="h-5 w-5" />;
      customClassName = 'bg-iilm-light border-iilm-primary text-iilm-primary';
      break;
    case 'info':
    default:
      icon = <Info className="h-5 w-5" />;
      customClassName = 'bg-blue-50 border-blue-200 text-blue-700';
      break;
  }

  return hookToast({
    title,
    description,
    duration,
    className: `${customClassName} ${className}`,
    action: (
      <div className={`flex items-center justify-center w-8 h-8 rounded-full ${
        type === 'success' ? 'bg-green-100' :
        type === 'error' ? 'bg-red-100' :
        type === 'processing' ? 'bg-iilm-primary bg-opacity-10' :
        'bg-blue-100'
      }`}>
        {icon}
      </div>
    ),
  });
};

/**
 * Success toast notification
 */
export const successToast = (title: string, description?: string, duration?: number) => {
  return customToast({
    title,
    description,
    duration,
    type: 'success',
  });
};

/**
 * Error toast notification
 */
export const errorToast = (title: string, description?: string, duration?: number) => {
  return customToast({
    title,
    description,
    duration,
    type: 'error',
  });
};

/**
 * Processing toast notification
 */
export const processingToast = (title: string, description?: string, duration?: number) => {
  return customToast({
    title,
    description,
    duration,
    type: 'processing',
  });
};

/**
 * Info toast notification
 */
export const infoToast = (title: string, description?: string, duration?: number) => {
  return customToast({
    title,
    description,
    duration,
    type: 'info',
  });
};
