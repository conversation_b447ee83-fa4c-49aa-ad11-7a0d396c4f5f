
@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 240 10% 98%;
    --foreground: 222.2 84% 4.9%;

    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;

    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;

    --primary: 220 53% 23%;
    --primary-foreground: 210 40% 98%;

    --secondary: 350 86% 31%;
    --secondary-foreground: 222.2 47.4% 11.2%;

    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;

    --accent: 260 40% 75%;
    --accent-foreground: 222.2 47.4% 11.2%;

    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;

    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 220 53% 23%;

    --radius: 0.75rem;

    /* Custom IILM colors */
    --iilm-primary: 220 53% 23%;
    --iilm-secondary: 350 86% 31%;
    --iilm-light: 220 53% 95%;
    --iilm-accent: 260 40% 75%;

    /* Beautiful gradients */
    --gradient-primary: linear-gradient(135deg, hsl(220 53% 23%) 0%, hsl(220 53% 30%) 100%);
    --gradient-secondary: linear-gradient(135deg, hsl(350 86% 31%) 0%, hsl(350 86% 38%) 100%);
    --gradient-accent: linear-gradient(135deg, hsl(260 40% 75%) 0%, hsl(260 40% 82%) 100%);
    --gradient-light: linear-gradient(135deg, hsl(220 53% 95%) 0%, hsl(220 53% 98%) 100%);
  }
}

/* Custom animations */
@keyframes fade-in {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scale-in {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-fade-in {
  animation: fade-in 0.5s ease-out;
}

.animate-slide-in {
  animation: slide-in 0.5s ease-out;
}

.animate-scale-in {
  animation: scale-in 0.3s ease-out;
}

/* Beautiful shadows */
.shadow-beautiful {
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

.shadow-card {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.shadow-hover {
  transition: box-shadow 0.3s ease;
}

.shadow-hover:hover {
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
}

/* Beautiful gradients */
.bg-gradient-primary {
  background: var(--gradient-primary);
}

.bg-gradient-secondary {
  background: var(--gradient-secondary);
}

.bg-gradient-accent {
  background: var(--gradient-accent);
}

.bg-gradient-light {
  background: var(--gradient-light);
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground;
  }
}

@layer components {
  .sentiment-positive {
    @apply text-green-600 bg-green-50 border-green-200;
  }

  .sentiment-neutral {
    @apply text-blue-600 bg-blue-50 border-blue-200;
  }

  .sentiment-negative {
    @apply text-red-600 bg-red-50 border-red-200;
  }

  .flag-item {
    @apply px-2 py-0.5 text-xs rounded-full;
  }

  .flag-frustration {
    @apply bg-red-50 text-red-600 border border-red-200;
  }

  .flag-confusion {
    @apply bg-amber-50 text-amber-600 border border-amber-200;
  }

  .flag-urgency {
    @apply bg-iilm-primary bg-opacity-10 text-iilm-primary border border-iilm-primary border-opacity-20;
  }

  .flag-satisfaction {
    @apply bg-iilm-secondary bg-opacity-10 text-iilm-secondary border border-iilm-secondary border-opacity-20;
  }

  .flag-positive {
    @apply bg-green-50 text-green-600 border border-green-200;
  }

  .flag-neutral {
    @apply bg-blue-50 text-blue-600 border border-blue-200;
  }

  .flag-negative {
    @apply bg-red-50 text-red-600 border border-red-200;
  }
}
