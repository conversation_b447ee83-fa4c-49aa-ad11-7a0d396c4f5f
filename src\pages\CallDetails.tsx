import React, { useState, useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import {
  ArrowUp,
  ArrowDown,
  FileText,
  Play,
  Loader,
  Clock,
  Phone,
  User,
  MessageSquare,
  ClipboardCheck, // Added for Quality Assessment title
} from "lucide-react";
import { CallData } from "@/services/mockData";
import { fetchCallDetails } from "@/services/callDetailsService";
import { useToast } from "@/components/ui/use-toast";

// Interface for segment structure if not already defined in CallData
interface Segment {
  speaker: string;
  text: string;
  start_time?: number;
  end_time?: number;
}

const CallDetails = () => {
  const { id } = useParams<{ id: string }>();
  const [call, setCall] = useState<CallData | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  useEffect(() => {
    // Scroll to top when component mounts
    window.scrollTo(0, 0);

    const loadCallDetails = async () => {
      if (!id) {
        setError("Call ID is missing");
        setIsLoading(false);
        return;
      }
      try {
        setIsLoading(true);
        const callData = await fetchCallDetails(id);
        setCall(callData);
        setError(null);
      } catch (err) {
        console.error("Error loading call details:", err);
        setError(
          err instanceof Error ? err.message : "Failed to load call details"
        );
        toast({
          title: "Error",
          description: "Failed to load call details. Please try again later.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };
    loadCallDetails();
  }, [id, toast]);

  // Helper for styled N/A
  const renderN_A = () => <span className="text-slate-400 italic">N/A</span>;

  if (isLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-50">
        <div className="container mx-auto px-4 py-8">
          <div className="flex flex-col items-center justify-center py-32">
            <div className="relative">
              <Loader className="h-12 w-12 animate-spin text-blue-600" />
              <div className="absolute inset-0 h-12 w-12 animate-ping rounded-full bg-blue-200 opacity-25"></div>
            </div>
            <p className="text-gray-600 mt-4 text-lg">
              Loading call details...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (error || !call) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-red-50 to-pink-50">
        <div className="container mx-auto px-4 py-8">
          <div className="text-center py-32">
            <div className="bg-red-100 rounded-full w-20 h-20 flex items-center justify-center mx-auto mb-6">
              <Phone className="h-10 w-10 text-red-600" />
            </div>
            <h1 className="text-3xl font-bold text-gray-900 mb-4">
              Call Not Found
            </h1>
            <p className="text-gray-600 mb-8 text-lg">
              {error ||
                "Sorry, we couldn't find the call details you're looking for."}
            </p>
            <Link to="/">
              <Button size="lg" className="px-8 py-3">
                Return to Dashboard
              </Button>
            </Link>
          </div>
        </div>
      </div>
    );
  }

  const callDate = new Date(call.dateTime).toLocaleString("en-US", {
    weekday: "long",
    year: "numeric",
    month: "long",
    day: "numeric",
    hour: "2-digit",
    minute: "2-digit",
  });

  const renderFlags = (flags: string[]) => {
    if (flags.length === 0)
      return <span className="text-gray-500 italic">No flags raised</span>;

    const flagStyles = {
      frustration: { bg: 'bg-red-100', text: 'text-red-800', dot: 'bg-red-500' },
      confusion: { bg: 'bg-yellow-100', text: 'text-yellow-800', dot: 'bg-yellow-500' },
      urgency: { bg: 'bg-orange-100', text: 'text-orange-800', dot: 'bg-orange-500' },
      satisfaction: { bg: 'bg-green-100', text: 'text-green-800', dot: 'bg-green-500' },
      abusive: { bg: 'bg-red-200', text: 'text-red-900', dot: 'bg-red-600' },
      urgent: { bg: 'bg-red-100', text: 'text-red-800', dot: 'bg-red-500' },
      followup: { bg: 'bg-yellow-100', text: 'text-yellow-800', dot: 'bg-yellow-500' },
      resolved: { bg: 'bg-green-100', text: 'text-green-800', dot: 'bg-green-500' },
      escalated: { bg: 'bg-orange-100', text: 'text-orange-800', dot: 'bg-orange-500' }
    };

    return (
      <div className="flex flex-wrap gap-2">
        {flags.map((flag, index) => {
          const style = flagStyles[flag.toLowerCase() as keyof typeof flagStyles] ||
                       { bg: 'bg-gray-100', text: 'text-gray-800', dot: 'bg-gray-500' };

          return (
            <span
              key={index}
              className={`inline-flex items-center px-3 py-1 rounded-full text-sm font-medium ${style.bg} ${style.text} shadow-sm`}
              title={`${flag.charAt(0).toUpperCase() + flag.slice(1)} flag detected in this call`}
            >
              <div className={`w-2 h-2 ${style.dot} rounded-full mr-2`}></div>
              {flag.charAt(0).toUpperCase() + flag.slice(1)}
            </span>
          );
        })}
      </div>
    );
  };

  const renderSentimentIndicator = () => {
    const sentimentConfig = {
      positive: {
        icon: ArrowUp,
        color: "text-green-600",
        bgColor: "bg-green-50",
        borderColor: "border-green-200",
        label: "Positive",
      },
      negative: {
        icon: ArrowDown,
        color: "text-red-600",
        bgColor: "bg-red-50",
        borderColor: "border-red-200",
        label: "Negative",
      },
      neutral: {
        icon: () => <div className="h-5 w-5 rounded-full bg-blue-500" />,
        color: "text-blue-600",
        bgColor: "bg-blue-50",
        borderColor: "border-blue-200",
        label: "Neutral",
      },
    };
    const config =
      sentimentConfig[call.sentiment as keyof typeof sentimentConfig] ||
      sentimentConfig.neutral;
    const IconComponent = config.icon;
    return (
      <div
        className={`flex items-center gap-2 px-3 py-2 rounded-lg border ${config.bgColor} ${config.borderColor}`}
      >
        <IconComponent className={`h-5 w-5 ${config.color}`} />
        <span className={`font-medium ${config.color}`}>{config.label}</span>
      </div>
    );
  };

  const formatTranscript = (transcript: string) => {
    if (call.segments && call.segments.length > 0) {
      return (
        <div className="space-y-6">
          {call.segments.map((segment, index) => {
            let speakerRole = "";
            if (call.speakerRoles && segment.speaker in call.speakerRoles) {
              speakerRole = call.speakerRoles[segment.speaker];
            }
            let layoutAlignRight;
            if (segment.speaker === "spk_1") {
              layoutAlignRight = true;
            } else if (segment.speaker === "spk_0") {
              layoutAlignRight = false;
            } else {
              layoutAlignRight =
                segment.speaker.toLowerCase().includes("counselor") ||
                segment.speaker.toLowerCase().includes("agent") ||
                speakerRole === "university_assistant";
            }

            const originalIsLeftSpeakerForAvatar =
              segment.speaker === "spk_0" ||
              segment.speaker.toLowerCase().includes("student") ||
              segment.speaker.toLowerCase() === "caller" ||
              speakerRole === "student";
            const originalIsRightSpeakerForAvatar =
              segment.speaker === "spk_1" ||
              segment.speaker.toLowerCase().includes("counselor") ||
              segment.speaker.toLowerCase().includes("agent") ||
              speakerRole === "university_assistant";

            const formatTime = (seconds?: number) => {
              if (seconds === undefined) return "";
              const mins = Math.floor(seconds / 60);
              const secs = Math.floor(seconds % 60);
              return `${mins.toString().padStart(2, "0")}:${secs
                .toString()
                .padStart(2, "0")}`;
            };
            const getSpeakerDisplay = () => {
              if (segment.speaker === "spk_0") return "Speaker 1";
              if (segment.speaker === "spk_1") return "Speaker 2";
              if (speakerRole)
                return speakerRole
                  .replace("_", " ")
                  .replace(/\b\w/g, (l) => l.toUpperCase());
              return (
                segment.speaker.charAt(0).toUpperCase() +
                segment.speaker.slice(1)
              );
            };
            const getSpeakerAvatar = () => {
              // Use colored dots for speakers
              return <div className="w-6 h-6 bg-blue-500 rounded-full"></div>;
            };
            return (
              <div
                key={index}
                className={`flex ${
                  layoutAlignRight ? "justify-end" : "justify-start"
                } items-start gap-3`}
              >
                {!layoutAlignRight && (
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-lg">
                    {getSpeakerAvatar()}
                  </div>
                )}
                <div
                  className={`max-w-[75%] ${
                    layoutAlignRight ? "order-first" : ""
                  }`}
                >
                  <div
                    className={`rounded-2xl px-4 py-3 shadow-sm border ${
                      layoutAlignRight
                        ? "bg-blue-600 text-white border-blue-600"
                        : "bg-white text-gray-900 border-gray-200"
                    }`}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <p
                        className={`font-semibold text-sm ${
                          layoutAlignRight ? "text-blue-100" : "text-gray-600"
                        }`}
                      >
                        {getSpeakerDisplay()}
                      </p>
                      {/* {segment.start_time !== undefined &&
                        segment.end_time !== undefined && (
                          <span
                            className={`text-xs flex items-center gap-1 ${
                              layoutAlignRight
                                ? "text-blue-200"
                                : "text-gray-500"
                            }`}
                          >
                            <Clock className="h-3 w-3" />
                            {formatTime(segment.start_time)} -{" "}
                            {formatTime(segment.end_time)}
                          </span>
                        )} */}
                    </div>
                    <p className="leading-relaxed">{segment.text}</p>
                  </div>
                </div>
                {layoutAlignRight && (
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-lg">
                    {getSpeakerAvatar()}
                  </div>
                )}
              </div>
            );
          })}
        </div>
      );
    }
    if (!transcript)
      return (
        <p className="text-gray-500 italic text-center py-8">
          No transcript available
        </p>
      );
    const lines = transcript.split("\n").filter((line) => line.trim());
    return (
      <div className="space-y-6">
        {lines.map((line, index) => {
          if (!line.trim()) return null;
          const lineLower = line.toLowerCase();
          let layoutAlignRightFallback;
          const isLineSpk1 = lineLower.startsWith("spk_1:");
          const isLineSpk0 = lineLower.startsWith("spk_0:");
          if (isLineSpk1) {
            layoutAlignRightFallback = true;
          } else if (isLineSpk0) {
            layoutAlignRightFallback = false;
          } else {
            layoutAlignRightFallback =
              lineLower.startsWith("counselor:") ||
              lineLower.startsWith("agent:");
          }
          const isOriginalLeftSpeaker =
            lineLower.startsWith("student:") || lineLower.startsWith("caller:");
          const isOriginalRightSpeaker =
            lineLower.startsWith("counselor:") ||
            lineLower.startsWith("agent:");
          const isSpeakerLine =
            isLineSpk0 ||
            isLineSpk1 ||
            isOriginalLeftSpeaker ||
            isOriginalRightSpeaker;

          if (isSpeakerLine) {
            const [speakerPart, ...textParts] = line.split(":");
            const text = textParts.join(":").trim();
            const getSpeakerDisplay = (speaker: string) => {
              const speakerNameLower = speaker.toLowerCase();
              if (speakerNameLower === "spk_0") return "Speaker 0";
              if (speakerNameLower === "spk_1") return "Speaker 1";
              return speaker.charAt(0).toUpperCase() + speaker.slice(1);
            };
            const getSpeakerAvatar = (isRightAligned: boolean) => {
              // Use colored dots for speakers
              return <div className="w-6 h-6 bg-blue-500 rounded-full"></div>;
            };
            return (
              <div
                key={index}
                className={`flex ${
                  layoutAlignRightFallback ? "justify-end" : "justify-start"
                } items-start gap-3`}
              >
                {!layoutAlignRightFallback && (
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-lg">
                    {getSpeakerAvatar(false)}
                  </div>
                )}
                <div
                  className={`max-w-[75%] ${
                    layoutAlignRightFallback ? "order-first" : ""
                  }`}
                >
                  <div
                    className={`rounded-2xl px-4 py-3 shadow-sm border ${
                      layoutAlignRightFallback
                        ? "bg-blue-600 text-white border-blue-600"
                        : "bg-white text-gray-900 border-gray-200"
                    }`}
                  >
                    <p
                      className={`font-semibold text-sm mb-2 ${
                        layoutAlignRightFallback
                          ? "text-blue-100"
                          : "text-gray-600"
                      }`}
                    >
                      {getSpeakerDisplay(speakerPart)}
                    </p>
                    <p className="leading-relaxed">{text}</p>
                  </div>
                </div>
                {layoutAlignRightFallback && (
                  <div className="flex-shrink-0 w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-lg">
                    {getSpeakerAvatar(true)}
                  </div>
                )}
              </div>
            );
          }
          return (
            <div key={index} className="text-center py-2">
              <p className="text-gray-600 italic">{line}</p>
            </div>
          );
        })}
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-8 animate-fade-in">
        {/* Header Section with Beautiful Gradient */}
        <div className="bg-gradient-primary rounded-2xl p-6 mb-8 shadow-beautiful text-white">
          <div className="flex flex-col md:flex-row md:justify-between md:items-center gap-4">
            <div>
              <h1 className="text-3xl font-bold mb-2">Call Details</h1>
              <p className="text-blue-100 flex items-center gap-2">
                <Phone className="h-5 w-5" />
                Call ID:{" "}
                <span className="font-mono bg-white/20 text-white px-3 py-1 rounded-lg text-sm backdrop-blur-sm">
                  {call.id}
                </span>
              </p>
            </div>
            <Link to="/">
              <Button
                variant="secondary"
                size="lg"
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm shadow-lg px-6"
              >
                ← Back to Dashboard
              </Button>
            </Link>
          </div>
        </div>

        {/* Stats Cards with Beautiful Styling */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
          {/* Call Information Card */}
          <Card className="bg-white rounded-2xl shadow-card hover:shadow-beautiful transition-all duration-300 border border-blue-100 animate-scale-in">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <Phone className="h-5 w-5 text-blue-600" />
                </div>
                Call Information
              </CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="space-y-3">
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Date & Time:</dt>
                  <dd className="text-sm font-medium text-slate-700 text-right max-w-[65%]">
                    {callDate}
                  </dd>
                </div>
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Duration:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.duration ? call.duration : renderN_A()}
                  </dd>
                </div>
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Direction:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.callDirection
                      ? call.callDirection.charAt(0).toUpperCase() +
                        call.callDirection.slice(1)
                      : renderN_A()}
                  </dd>
                </div>
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Source:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.callSource ? call.callSource : renderN_A()}
                  </dd>
                </div>
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Group:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.groupName ? call.groupName : renderN_A()}
                  </dd>
                </div>
              </dl>
            </CardContent>
          </Card>

          {/* Participants Card */}
          <Card className="bg-white rounded-2xl shadow-card hover:shadow-beautiful transition-all duration-300 border border-green-100 animate-scale-in">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <div className="p-2 bg-green-100 rounded-lg">
                  <User className="h-5 w-5 text-green-600" />
                </div>
                Participants
              </CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="space-y-3">
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Counselor:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.counselor ? call.counselor : renderN_A()}
                  </dd>
                </div>
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Student:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.studentNumber ? call.studentNumber : renderN_A()}
                  </dd>
                </div>
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Transcription ID:</dt>
                  <dd
                    className="text-xs font-mono bg-white/70 text-slate-600 px-2 py-1 rounded truncate max-w-[120px] border border-slate-200"
                    title={call.transcriptionId || "N/A"}
                  >
                    {call.transcriptionId ? call.transcriptionId : renderN_A()}
                  </dd>
                </div>
              </dl>
            </CardContent>
          </Card>

          {/* Analysis Results Card */}
          <Card className="bg-white rounded-2xl shadow-card hover:shadow-beautiful transition-all duration-300 border border-purple-100 animate-scale-in">
            <CardHeader className="pb-3">
              <CardTitle className="text-lg font-semibold text-gray-800 flex items-center gap-2">
                <div className="p-2 bg-purple-100 rounded-lg">
                  <MessageSquare className="h-5 w-5 text-purple-600" />
                </div>
                Analysis Results
              </CardTitle>
            </CardHeader>
            <CardContent>
              <dl className="space-y-4">
                {" "}
                {/* Increased space slightly */}
                <div>
                  <dt className="text-sm text-slate-500 mb-1">Sentiment:</dt>
                  <dd>{renderSentimentIndicator()}</dd>
                </div>
                {call.sentimentScore !== undefined && (
                  <div className="flex justify-between items-center">
                    <dt className="text-sm text-slate-500">Score:</dt>
                    <dd className="text-sm font-medium text-slate-700">
                      {(call.sentimentScore * 100).toFixed(1)}%
                    </dd>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Info Accuracy:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.infoAccuracy !== undefined && call.infoAccuracy !== null && typeof call.infoAccuracy === 'number'
                      ? (
                        <>
                          {`${Math.round(call.infoAccuracy)}%`}
                          <span
                            className={`ml-2 text-xs px-2 py-0.5 rounded-full ${
                              call.infoAccuracy >= 90
                                ? "bg-green-100 text-green-700 border border-green-200"
                                : call.infoAccuracy >= 75
                                ? "bg-yellow-100 text-yellow-700 border border-yellow-200"
                                : "bg-red-100 text-red-700 border border-red-200"
                            }`}
                          >
                            {call.infoAccuracy >= 90
                              ? "Good"
                              : call.infoAccuracy >= 75
                              ? "Average"
                              : "Poor"}
                          </span>
                        </>
                      )
                      : renderN_A()}
                  </dd>
                </div>
                {call.relevantCategory && (
                  <div className="flex justify-between items-center">
                    <dt className="text-sm text-slate-500">Category:</dt>
                    <dd className="text-sm font-medium text-slate-700">
                      <span className="px-2 py-1 bg-blue-100 text-blue-800 border border-blue-200 rounded-md text-xs">
                        {call.relevantCategory}
                      </span>
                    </dd>
                  </div>
                )}
                {call.conversationTone && (
                  <div className="flex justify-between items-center">
                    <dt className="text-sm text-slate-500">Tone:</dt>
                    <dd className="text-sm font-medium text-slate-700">
                      <span className="px-2 py-1 bg-purple-100 text-purple-800 border border-purple-200 rounded-md text-xs">
                        {call.conversationTone}
                      </span>
                    </dd>
                  </div>
                )}
                <div className="flex justify-between items-center">
                  <dt className="text-sm text-slate-500">Flags:</dt>
                  <dd className="text-sm font-medium text-slate-700">
                    {call.flags.length}
                  </dd>
                </div>
              </dl>
            </CardContent>
          </Card>
        </div>

        {/* Summary and Analysis Section */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8 mb-8">
          <Card className="bg-white rounded-2xl shadow-card hover:shadow-beautiful transition-all duration-300 border border-gray-100 animate-fade-in">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-800">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <FileText className="h-6 w-6 text-blue-600" />
                </div>
                Call Summary
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6 text-slate-700">
              <div className="bg-slate-50 p-5 rounded-xl border border-slate-200">
                <p className="leading-relaxed">
                  {call.sentimentSummary || call.summary || renderN_A()}
                </p>
              </div>

              {call.keyPoints && call.keyPoints.length > 0 && (
                <div>
                  <h4 className="text-md font-semibold mb-3 text-gray-600">
                    Key Discussion Points:
                  </h4>
                  <ul className="space-y-2.5 pl-1">
                    {call.keyPoints.map((point, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-blue-500 rounded-full mt-[7px] flex-shrink-0 shadow-sm"></div>
                        <span>{point}</span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {call.conversationTopic && (
                <div className="flex items-center gap-2 pt-2">
                  <span className="text-sm font-semibold text-gray-600">
                    Primary Topic:
                  </span>
                  <span className="px-3 py-1 bg-blue-100 text-blue-800 border border-blue-200 rounded-full text-sm font-medium shadow-sm">
                    {call.conversationTopic}
                  </span>
                </div>
              )}

              {call.actionItems && call.actionItems.length > 0 && (
                <div className="pt-2">
                  <h4 className="text-md font-semibold mb-3 text-gray-600">
                    Action Items:
                  </h4>
                  <ul className="space-y-2.5 pl-1">
                    {call.actionItems.map((item, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-green-500 rounded-full mt-[7px] flex-shrink-0 shadow-sm"></div>
                        <span className="text-blue-700 font-medium">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              )}
            </CardContent>
          </Card>
          <Card className="bg-white rounded-2xl shadow-card hover:shadow-beautiful transition-all duration-300 border border-gray-100 animate-fade-in">
            <CardHeader>
              <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-800">
                <div className="p-2 bg-gray-100 rounded-lg">
                  <ClipboardCheck className="h-6 w-6 text-gray-600" />
                </div>
                Quality Assessment
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              <div>
                <h4 className="text-md font-semibold mb-3 text-gray-600">
                  Status Flags:
                </h4>
                {renderFlags(call.flags)}
              </div>
              <Separator className="my-4" />
              <div>
                <h4 className="text-md font-semibold mb-3 text-gray-600">
                  Information Gaps:
                </h4>
                {call.missedInfo && call.missedInfo.length > 0 ? (
                  <ul className="space-y-2.5 pl-1 text-slate-700">
                    {call.missedInfo.map((item, index) => (
                      <li key={index} className="flex items-start gap-3">
                        <div className="w-2 h-2 bg-red-500 rounded-full mt-[7px] flex-shrink-0 shadow-sm"></div>
                        <span className="text-red-600">{item}</span>
                      </li>
                    ))}
                  </ul>
                ) : (
                  <div className="flex items-center gap-2 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm"></div>
                    <span>No information gaps identified</span>
                  </div>
                )}
              </div>
              <Separator className="my-4" />
              <div>
                <h4 className="text-md font-semibold mb-3 text-gray-600">
                  Accuracy Issues:
                </h4>
                {call.incorrectInfo && call.incorrectInfo.length > 0 ? (
                  <div className="space-y-3">
                    {Array.isArray(call.incorrectInfo) &&
                    typeof call.incorrectInfo[0] === "string" ? (
                      (call.incorrectInfo as string[]).map((item, index) => (
                        <div
                          key={index}
                          className="flex items-start gap-3 text-red-600"
                        >
                          <div className="w-2 h-2 bg-red-500 rounded-full mt-[7px] flex-shrink-0 shadow-sm"></div>
                          <span>{item}</span>
                        </div>
                      ))
                    ) : Array.isArray(call.incorrectInfo) &&
                      typeof call.incorrectInfo[0] === "object" &&
                      "provided" in (call.incorrectInfo[0] || {}) ? (
                      (
                        call.incorrectInfo as {
                          provided: string;
                          correct: string;
                        }[]
                      ).map((item, index) => (
                        <div
                          key={index}
                          className="bg-red-50 p-4 rounded-lg border border-red-200 shadow-sm"
                        >
                          <div className="text-red-700 mb-1.5">
                            <span className="font-semibold">Stated: </span>
                            <span className="italic">"{item.provided}"</span>
                          </div>
                          <div className="text-green-700">
                            <span className="font-semibold">Should be: </span>
                            <span className="italic">"{item.correct}"</span>
                          </div>
                        </div>
                      ))
                    ) : (
                      <span className="text-red-600">{renderN_A()}</span>
                    )}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm"></div>
                    <span>No accuracy issues detected</span>
                  </div>
                )}
              </div>
              <Separator className="my-4" />
              <div>
                <h4 className="text-md font-semibold mb-3 text-gray-600">
                  Incomplete Information:
                </h4>
                {call.incompleteInfo && call.incompleteInfo.length > 0 ? (
                  <div className="space-y-3">
                    {call.incompleteInfo.map((item, index) => (
                      <div
                        key={index}
                        className="bg-orange-50 p-4 rounded-lg border border-orange-200 shadow-sm"
                      >
                        <div className="text-orange-700 mb-1.5">
                          <span className="font-semibold">Provided: </span>
                          <span className="italic">"{item.provided}"</span>
                        </div>
                        <div className="text-blue-700">
                          <span className="font-semibold">Complete info: </span>
                          <span className="italic">"{item.complete}"</span>
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="flex items-center gap-2 text-green-600">
                    <div className="w-2 h-2 bg-green-500 rounded-full shadow-sm"></div>
                    <span>All information provided was complete</span>
                  </div>
                )}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Transcript Section */}
        <Card className="bg-white rounded-2xl shadow-card hover:shadow-beautiful transition-all duration-300 border border-gray-100 animate-fade-in">
          <CardHeader className="bg-gradient-to-r from-slate-50 to-gray-50 rounded-t-2xl border-b border-slate-200">
            <div className="flex flex-col md:flex-row md:items-center justify-between gap-4">
              <CardTitle className="flex items-center gap-3 text-xl font-semibold text-gray-800">
                <div className="p-2 bg-blue-100 rounded-lg">
                  <MessageSquare className="h-6 w-6 text-blue-600" />
                </div>
                Call Recording & Transcript
              </CardTitle>
              {call.audioUrl ? (
                <div className="bg-white p-3 rounded-lg shadow-md border border-slate-200" style={{ minWidth: '350px' }}>
                  <div className="flex items-center gap-2 mb-2">
                    <Play className="h-4 w-4 text-blue-600" />
                    <span className="text-sm font-medium text-gray-700">Audio Recording</span>
                  </div>
                  <audio
                    controls
                    className="w-full max-w-md h-10"
                    preload="metadata"
                  >
                    <source src={call.audioUrl} type="audio/wav" />
                    <source src={call.audioUrl} type="audio/mpeg" />
                    <source src={call.audioUrl} type="audio/mp3" />
                    Your browser does not support the audio element.
                  </audio>
                  <div className="mt-1">
                    <a
                      href={call.audioUrl}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-xs text-blue-600 hover:text-blue-800 underline"
                    >
                      Open in new tab
                    </a>
                  </div>
                </div>
              ) : (
                <Button
                  size="sm"
                  variant="outline"
                  className="flex items-center gap-2 shadow-sm"
                  disabled
                >
                  <Play className="h-4 w-4" /> No Recording Available
                </Button>
              )}
            </div>
          </CardHeader>
          <CardContent className="p-0 sm:p-2">
            {" "}
            {/* Adjusted padding */}
            <div className="bg-slate-50 rounded-xl p-4 sm:p-6 max-h-[500px] overflow-y-auto">
              {formatTranscript(call.transcript || "")}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
};

export default CallDetails;
