import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useToast } from '@/components/ui/use-toast';
import { Loader, Edit, Trash2, Plus, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import {
  fetchCounselorEmails,
  addCounselor,
  updateCounselor,
  deleteCounselor,
  validateEmail,
  validateCounselorName,
  CounselorEmailContact
} from '@/services/counselorEmailService';

const CounselorEmailManagement = () => {
  const { toast } = useToast();
  const navigate = useNavigate();

  // State for counselor list
  const [counselors, setCounselors] = useState<CounselorEmailContact[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // State for add form
  const [newCounselorName, setNewCounselorName] = useState('');
  const [newCounselorEmail, setNewCounselorEmail] = useState('');
  const [isAdding, setIsAdding] = useState(false);

  // State for update dialog
  const [updateDialog, setUpdateDialog] = useState<{
    open: boolean;
    counselor: CounselorEmailContact | null;
    email: string;
    isUpdating: boolean;
  }>({
    open: false,
    counselor: null,
    email: '',
    isUpdating: false
  });

  // State for delete confirmation
  const [deleteDialog, setDeleteDialog] = useState<{
    open: boolean;
    counselor: CounselorEmailContact | null;
    isDeleting: boolean;
  }>({
    open: false,
    counselor: null,
    isDeleting: false
  });

  // Load counselors on component mount
  useEffect(() => {
    loadCounselors();
  }, []);

  const loadCounselors = async () => {
    try {
      console.log('Loading counselors...');
      setIsLoading(true);
      const data = await fetchCounselorEmails();
      console.log('Counselors loaded:', data);
      setCounselors(data);
    } catch (error) {
      console.error('Error loading counselors:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to load counselors",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleAddCounselor = async () => {
    console.log('handleAddCounselor called with:', { newCounselorName, newCounselorEmail });

    // Validate inputs
    if (!validateCounselorName(newCounselorName)) {
      console.log('Validation failed: Invalid counselor name');
      toast({
        title: "Invalid Name",
        description: "Please enter a valid counselor name (1-100 characters)",
        variant: "destructive",
      });
      return;
    }

    if (!validateEmail(newCounselorEmail)) {
      console.log('Validation failed: Invalid email');
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address",
        variant: "destructive",
      });
      return;
    }

    // Check if counselor already exists
    if (counselors.some(c => c.name.toLowerCase() === newCounselorName.trim().toLowerCase())) {
      console.log('Validation failed: Counselor already exists');
      toast({
        title: "Counselor Exists",
        description: "A counselor with this name already exists",
        variant: "destructive",
      });
      return;
    }

    try {
      console.log('Starting to add counselor...');
      setIsAdding(true);
      await addCounselor(newCounselorName.trim(), newCounselorEmail.trim());
      console.log('Counselor added successfully');

      toast({
        title: "Success",
        description: "Counselor added successfully",
      });

      // Clear form and reload data
      setNewCounselorName('');
      setNewCounselorEmail('');
      await loadCounselors();
    } catch (error) {
      console.error('Error adding counselor:', error);
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to add counselor",
        variant: "destructive",
      });
    } finally {
      setIsAdding(false);
    }
  };

  const handleUpdateCounselor = async () => {
    if (!updateDialog.counselor) return;

    if (!validateEmail(updateDialog.email)) {
      toast({
        title: "Invalid Email",
        description: "Please enter a valid email address",
        variant: "destructive",
      });
      return;
    }

    try {
      setUpdateDialog(prev => ({ ...prev, isUpdating: true }));
      await updateCounselor(updateDialog.counselor.name, updateDialog.email.trim());

      toast({
        title: "Success",
        description: "Counselor updated successfully",
      });

      // Close dialog and reload data
      setUpdateDialog({ open: false, counselor: null, email: '', isUpdating: false });
      await loadCounselors();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to update counselor",
        variant: "destructive",
      });
    } finally {
      setUpdateDialog(prev => ({ ...prev, isUpdating: false }));
    }
  };

  const handleDeleteCounselor = async () => {
    if (!deleteDialog.counselor) return;

    try {
      setDeleteDialog(prev => ({ ...prev, isDeleting: true }));
      await deleteCounselor(deleteDialog.counselor.name);

      toast({
        title: "Success",
        description: "Counselor deleted successfully",
      });

      // Close dialog and reload data
      setDeleteDialog({ open: false, counselor: null, isDeleting: false });
      await loadCounselors();
    } catch (error) {
      toast({
        title: "Error",
        description: error instanceof Error ? error.message : "Failed to delete counselor",
        variant: "destructive",
      });
    } finally {
      setDeleteDialog(prev => ({ ...prev, isDeleting: false }));
    }
  };

  const openUpdateDialog = (counselor: CounselorEmailContact) => {
    setUpdateDialog({
      open: true,
      counselor,
      email: counselor.email,
      isUpdating: false
    });
  };

  const openDeleteDialog = (counselor: CounselorEmailContact) => {
    setDeleteDialog({
      open: true,
      counselor,
      isDeleting: false
    });
  };

  const formatDateTime = (dateTimeStr: string) => {
    try {
      const date = new Date(dateTimeStr);
      if (isNaN(date.getTime())) {
        return dateTimeStr;
      }
      return date.toLocaleString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
      });
    } catch (error) {
      return dateTimeStr;
    }
  };

  return (
    <div className="container mx-auto px-4 py-8 animate-fade-in">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Button
          variant="ghost"
          onClick={() => navigate('/')}
          className="text-iilm-primary hover:text-iilm-secondary"
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Back to Dashboard
        </Button>
        <h1 className="text-2xl font-bold text-iilm-primary">Counselor Email Management</h1>
      </div>

      {/* Add New Counselor Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Plus className="h-5 w-5" />
            Add New Counselor
          </CardTitle>
          <CardDescription>
            Add a new counselor with their email address for communication purposes.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="space-y-2">
              <Label htmlFor="counselor-name">Counselor Name</Label>
              <Input
                id="counselor-name"
                placeholder="Enter counselor name"
                value={newCounselorName}
                onChange={(e) => setNewCounselorName(e.target.value)}
                disabled={isAdding}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="counselor-email">Email Address</Label>
              <Input
                id="counselor-email"
                type="email"
                placeholder="Enter email address"
                value={newCounselorEmail}
                onChange={(e) => setNewCounselorEmail(e.target.value)}
                disabled={isAdding}
              />
            </div>
            <div className="flex items-end">
              <Button
                onClick={handleAddCounselor}
                disabled={isAdding || !newCounselorName.trim() || !newCounselorEmail.trim()}
                className="w-full bg-iilm-primary text-white hover:bg-iilm-secondary"
              >
                {isAdding ? (
                  <>
                    <Loader className="h-4 w-4 mr-2 animate-spin" />
                    Adding...
                  </>
                ) : (
                  <>
                    <Plus className="h-4 w-4 mr-2" />
                    Add Counselor
                  </>
                )}
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Counselor List Section */}
      <Card>
        <CardHeader>
          <CardTitle>Counselor List</CardTitle>
          <CardDescription>
            Manage existing counselor email contacts. You can update email addresses or remove counselors.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-12">
              <Loader className="h-8 w-8 animate-spin text-iilm-primary mb-2" />
              <p className="text-gray-600">Loading counselors...</p>
            </div>
          ) : (
            <div className="rounded-md border">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Name</TableHead>
                    <TableHead>Email</TableHead>
                    <TableHead>Created Date</TableHead>
                    <TableHead>Updated Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {counselors.length > 0 ? (
                    counselors.map((counselor, index) => {
                      console.log(`Rendering counselor ${index}:`, counselor);
                      return (
                        <TableRow key={counselor.id || counselor.name || index} className="hover:bg-gray-50">
                          <TableCell className="font-medium">
                            {counselor.name || 'N/A'}
                          </TableCell>
                          <TableCell>{counselor.email || 'N/A'}</TableCell>
                          <TableCell>{formatDateTime(counselor.created_at)}</TableCell>
                          <TableCell>{formatDateTime(counselor.updated_at)}</TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openUpdateDialog(counselor)}
                                className="text-blue-600 hover:text-blue-800"
                                title="Update email"
                              >
                                <Edit className="h-4 w-4" />
                              </Button>
                              <Button
                                variant="ghost"
                                size="sm"
                                onClick={() => openDeleteDialog(counselor)}
                                className="text-red-600 hover:text-red-800"
                                title="Delete counselor"
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      );
                    })
                  ) : (
                    <TableRow>
                      <TableCell colSpan={5} className="h-24 text-center">
                        No counselors found. Add your first counselor above.
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Update Dialog */}
      <Dialog open={updateDialog.open} onOpenChange={(open) =>
        !updateDialog.isUpdating && setUpdateDialog(prev => ({ ...prev, open }))
      }>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Update Counselor Email</DialogTitle>
            <DialogDescription>
              Update the email address for {updateDialog.counselor?.name}
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4 py-4">
            <div className="space-y-2">
              <Label htmlFor="update-email">Email Address</Label>
              <Input
                id="update-email"
                type="email"
                value={updateDialog.email}
                onChange={(e) => setUpdateDialog(prev => ({ ...prev, email: e.target.value }))}
                disabled={updateDialog.isUpdating}
                placeholder="Enter new email address"
              />
            </div>
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setUpdateDialog({ open: false, counselor: null, email: '', isUpdating: false })}
              disabled={updateDialog.isUpdating}
            >
              Cancel
            </Button>
            <Button
              onClick={handleUpdateCounselor}
              disabled={updateDialog.isUpdating || !updateDialog.email.trim()}
              className="bg-iilm-primary text-white hover:bg-iilm-secondary"
            >
              {updateDialog.isUpdating ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Updating...
                </>
              ) : (
                'Update Email'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialog.open} onOpenChange={(open) =>
        !deleteDialog.isDeleting && setDeleteDialog(prev => ({ ...prev, open }))
      }>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Delete Counselor</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete {deleteDialog.counselor?.name}? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setDeleteDialog({ open: false, counselor: null, isDeleting: false })}
              disabled={deleteDialog.isDeleting}
            >
              Cancel
            </Button>
            <Button
              variant="destructive"
              onClick={handleDeleteCounselor}
              disabled={deleteDialog.isDeleting}
            >
              {deleteDialog.isDeleting ? (
                <>
                  <Loader className="h-4 w-4 mr-2 animate-spin" />
                  Deleting...
                </>
              ) : (
                'Delete'
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
};

export default CounselorEmailManagement;
