
import { useState, useEffect } from "react";
import { Route, Routes, useNavigate, useLocation } from 'react-router-dom';
import Login from './Login';
import Dashboard from './Dashboard';
import CallDetails from './CallDetails';
import CounselorEmailManagement from './CounselorEmailManagement';
import UniversityInfoManagement from './UniversityInfoManagement';
import Header from '@/components/layout/Header';
import { getAuthToken, removeAuthToken, isAuthenticated } from '@/services/authService';

const Index = () => {
  const [isLoggedIn, setIsLoggedIn] = useState(false);
  const [username, setUsername] = useState('');
  const navigate = useNavigate();
  const location = useLocation();

  // Check if user is already logged in when component mounts
  useEffect(() => {
    // Check for token instead of username
    if (isAuthenticated()) {
      // Try to get username from localStorage for display purposes
      const storedUser = localStorage.getItem('iilm_user');
      if (storedUser) {
        setUsername(storedUser);
      }
      setIsLoggedIn(true);
    } else if (location.pathname !== '/login') {
      // Redirect to login if not logged in and not already on login page
      navigate('/login');
    }
  }, [navigate, location.pathname]);

  // Handle login
  const handleLogin = (username: string) => {
    setIsLoggedIn(true);
    setUsername(username);
    localStorage.setItem('iilm_user', username);
  };

  // Handle logout
  const handleLogout = () => {
    setIsLoggedIn(false);
    setUsername('');
    removeAuthToken();
    localStorage.removeItem('iilm_user');
    navigate('/login');
  };

  // Global error handler for 401 errors
  useEffect(() => {
    const handleUnauthorized = () => {
      console.warn('Global 401 handler triggered - logging out user');
      handleLogout();
    };

    // Listen for custom 401 events
    window.addEventListener('unauthorized', handleUnauthorized);

    return () => {
      window.removeEventListener('unauthorized', handleUnauthorized);
    };
  }, []);

  // Check if current path is login page
  const isLoginPage = location.pathname === '/login';

  return (
    <div className="min-h-screen flex flex-col bg-gray-50">
      {!isLoginPage && <Header isLoggedIn={isLoggedIn} onLogout={handleLogout} />}

      <main className="flex-grow">
        <Routes>
          <Route path="/login" element={<Login onLogin={handleLogin} />} />
          <Route path="/" element={isLoggedIn ? <Dashboard /> : <Login onLogin={handleLogin} />} />
          <Route path="/call/:id" element={isLoggedIn ? <CallDetails /> : <Login onLogin={handleLogin} />} />
          <Route path="/counselor-emails" element={isLoggedIn ? <CounselorEmailManagement /> : <Login onLogin={handleLogin} />} />
          <Route path="/university-info" element={isLoggedIn ? <UniversityInfoManagement /> : <Login onLogin={handleLogin} />} />
        </Routes>
      </main>
    </div>
  );
};

export default Index;
