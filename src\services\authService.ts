
import { BASE_URL } from '@/config/env';

interface LoginResponse {
  access_token: string;
  token_type: string;
}

/**
 * Authenticates a user with the API
 * @param username User's email address
 * @param password User's password
 * @returns Promise with login response containing access token
 */
export const loginUser = async (username: string, password: string): Promise<LoginResponse> => {
  const url = `${BASE_URL}/api/v1/auth/login`;

  // Create form data with required parameters
  const formData = new URLSearchParams();
  formData.append('grant_type', 'password');
  formData.append('username', username);
  formData.append('password', password);
  formData.append('scope', '');
  formData.append('client_id', 'string');
  formData.append('client_secret', 'string');

  const response = await fetch(url, {
    method: 'POST',
    headers: {
      'accept': 'application/json',
      'Content-Type': 'application/x-www-form-urlencoded'
    },
    body: formData
  });

  if (!response.ok) {
    const errorData = await response.json().catch(() => ({ detail: 'Login failed' }));
    throw new Error(errorData.detail || `Login failed with status ${response.status}`);
  }

  return response.json();
};

/**
 * Stores the authentication token in local storage
 * @param token JWT access token
 */
export const setAuthToken = (token: string): void => {
  localStorage.setItem('access_token', token);
};

/**
 * Retrieves the authentication token from local storage
 * @returns The stored JWT token or null if not found
 */
export const getAuthToken = (): string | null => {
  return localStorage.getItem('access_token');
};

/**
 * Removes the authentication token from local storage
 */
export const removeAuthToken = (): void => {
  localStorage.removeItem('access_token');
};

/**
 * Checks if the user is authenticated
 * @returns True if an authentication token exists
 */
export const isAuthenticated = (): boolean => {
  return !!getAuthToken();
};

/**
 * Logs the current authentication status and token details
 * Useful for debugging authentication issues
 */
export const logAuthStatus = (): void => {
  const token = getAuthToken();
  console.log('Authentication status:', !!token);

  if (token) {
    console.log('Token exists with length:', token.length);
    console.log('Token first 10 chars:', token.substring(0, 10) + '...');

    try {
      // Try to decode the JWT token (just the payload part)
      const payload = token.split('.')[1];
      const decodedPayload = atob(payload);
      const payloadData = JSON.parse(decodedPayload);

      console.log('Token payload:', payloadData);

      // Check if token is expired
      if (payloadData.exp) {
        const expiryDate = new Date(payloadData.exp * 1000);
        const now = new Date();
        console.log('Token expires at:', expiryDate.toISOString());
        console.log('Token is expired:', expiryDate < now);
      }
    } catch (error) {
      console.error('Error decoding token:', error);
    }
  } else {
    console.warn('No authentication token found');
  }
};
