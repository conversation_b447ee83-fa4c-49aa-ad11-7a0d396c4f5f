import { httpClient } from './httpClient';

export interface SchoolInfoCategory {
  id: string;
  category: string;
  content: string;
  created_at: string;
  updated_at: string;
}

export interface SchoolInfoRequest {
  category: string;
  content: string;
}

export interface SchoolInfoResponse {
  categories: SchoolInfoCategory[];
}

/**
 * Fetches all school information categories
 * @returns Promise with array of school info categories
 */
export const fetchSchoolInfoCategories = async (): Promise<SchoolInfoCategory[]> => {
  try {
    console.log('Fetching school info categories from API...');
    const response = await httpClient.get('/api/v1/school-info/categories');
    console.log('School info categories API response:', response);

    if (!response || !response.categories) {
      console.warn('Empty or invalid response from school info API');
      return [];
    }

    return response.categories;
  } catch (error) {
    console.error('Failed to fetch school info categories:', error);
    throw new Error('Failed to fetch university information. Please try again later.');
  }
};

/**
 * Creates or updates a school information category
 * @param categoryData The category data to create/update
 * @returns Promise with the created/updated category
 */
export const createOrUpdateSchoolInfo = async (categoryData: SchoolInfoRequest): Promise<SchoolInfoCategory> => {
  try {
    console.log('Creating/updating school info category:', categoryData);
    const response = await httpClient.post('/api/v1/school-info/categories', categoryData);
    console.log('Create/update school info API response:', response);

    if (!response) {
      throw new Error('Empty response from server');
    }

    return response;
  } catch (error) {
    console.error('Failed to create/update school info category:', error);
    throw new Error('Failed to save university information. Please try again later.');
  }
};

/**
 * Updates a specific school information category by category name
 * @param categoryName The name of the category to update
 * @param content The new content for the category
 * @returns Promise with the updated category
 */
export const updateSchoolInfoCategory = async (categoryName: string, content: string): Promise<SchoolInfoCategory> => {
  try {
    console.log(`Updating school info category: ${categoryName}`);
    const response = await httpClient.put(`/api/v1/school-info/categories/${encodeURIComponent(categoryName)}`, {
      content
    });
    console.log('Update school info API response:', response);

    if (!response) {
      throw new Error('Empty response from server');
    }

    return response;
  } catch (error) {
    console.error('Failed to update school info category:', error);
    throw new Error('Failed to update university information. Please try again later.');
  }
};

/**
 * Deletes a school information category by category name
 * @param categoryName The name of the category to delete
 * @returns Promise that resolves when deletion is complete
 */
export const deleteSchoolInfoCategory = async (categoryName: string): Promise<void> => {
  try {
    console.log(`Deleting school info category: ${categoryName}`);
    const encodedCategoryName = encodeURIComponent(categoryName);
    const deleteUrl = `/api/v1/school-info/categories/${encodedCategoryName}`;
    console.log(`Delete URL: ${deleteUrl}`);

    await httpClient.delete(deleteUrl);
    console.log('Delete school info API request completed successfully');
  } catch (error) {
    console.error('Failed to delete school info category:', error);
    console.error('Category name:', categoryName);
    console.error('Encoded category name:', encodeURIComponent(categoryName));
    throw new Error('Failed to delete university information. Please try again later.');
  }
};
