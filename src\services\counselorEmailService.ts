import { httpClient } from './httpClient';

// Interface for counselor email contact
export interface CounselorEmailContact {
  id: string;
  name: string;
  email: string;
  created_at: string;
  updated_at: string;
}

// Interface for API responses
export interface CounselorEmailListResponse {
  contacts: CounselorEmailContact[];
}

export interface BulkUpsertRequest {
  contacts: Record<string, string>; // { "alice123": "<EMAIL>" }
}

export interface UpdateCounselorRequest {
  email: string;
}

/**
 * Fetches all counselor email contacts
 * @returns Promise with list of counselor email contacts
 */
export const fetchCounselorEmails = async (): Promise<CounselorEmailContact[]> => {
  try {
    console.log('Fetching counselor email contacts...');
    const response = await httpClient.get('/api/v1/email-contacts');
    console.log('Counselor emails API response:', response);

    // Handle different response formats
    if (!response) {
      console.warn('Empty response from counselor emails API');
      return [];
    }

    // Check if response has contacts array
    if (response.contacts && Array.isArray(response.contacts)) {
      console.log('Found contacts array with', response.contacts.length, 'items');
      return response.contacts;
    }

    // Check if response is directly an array
    if (Array.isArray(response)) {
      console.log('Response is directly an array with', response.length, 'items');
      return response;
    }

    console.warn('Invalid response format from counselor emails API:', response);
    return [];
  } catch (error) {
    console.error('Error fetching counselor emails:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to fetch counselor emails');
  }
};

/**
 * Adds a new counselor using the bulk-upsert endpoint
 * @param counselorName The name of the counselor
 * @param email The email address of the counselor
 * @returns Promise that resolves when the counselor is added
 */
export const addCounselor = async (counselorName: string, email: string): Promise<void> => {
  try {
    console.log(`Adding counselor: ${counselorName} with email: ${email}`);

    const requestData: BulkUpsertRequest = {
      contacts: {
        [counselorName]: email
      }
    };

    console.log('Request data for bulk-upsert:', requestData);
    const response = await httpClient.post('/api/v1/email-contacts/bulk-upsert', requestData);
    console.log('Add counselor API response:', response);
  } catch (error) {
    console.error('Error adding counselor:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to add counselor');
  }
};

/**
 * Updates an existing counselor's email
 * @param counselorName The name of the counselor to update
 * @param email The new email address
 * @returns Promise that resolves when the counselor is updated
 */
export const updateCounselor = async (counselorName: string, email: string): Promise<void> => {
  try {
    console.log(`Updating counselor: ${counselorName} with new email: ${email}`);

    const requestData: UpdateCounselorRequest = {
      email: email
    };

    console.log('Request data for update:', requestData);
    const response = await httpClient.put(`/api/v1/email-contacts/${encodeURIComponent(counselorName)}`, requestData);
    console.log('Update counselor API response:', response);
  } catch (error) {
    console.error('Error updating counselor:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to update counselor');
  }
};

/**
 * Deletes a counselor email contact
 * @param counselorName The name of the counselor to delete
 * @returns Promise that resolves when the counselor is deleted
 */
export const deleteCounselor = async (counselorName: string): Promise<void> => {
  try {
    console.log(`Deleting counselor: ${counselorName}`);

    const response = await httpClient.delete(`/api/v1/email-contacts/${encodeURIComponent(counselorName)}`);
    console.log('Delete counselor API response:', response);
  } catch (error) {
    console.error('Error deleting counselor:', error);
    throw new Error(error instanceof Error ? error.message : 'Failed to delete counselor');
  }
};

/**
 * Validates email format
 * @param email The email to validate
 * @returns True if email is valid, false otherwise
 */
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

/**
 * Validates counselor name
 * @param name The name to validate
 * @returns True if name is valid, false otherwise
 */
export const validateCounselorName = (name: string): boolean => {
  return name.trim().length > 0 && name.trim().length <= 100;
};
