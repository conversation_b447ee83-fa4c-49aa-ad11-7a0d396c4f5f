
import React, { useState } from 'react';
import { format } from 'date-fns';
import { Calendar as CalendarIcon } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import { triggerDailyCallProcessing, formatDateForTrigger } from '@/services/callAnalysisService';
import { useToast } from '@/hooks/use-toast';
import { processingToast, errorToast } from '@/components/ui/custom-toast';
import { Loader } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ProcessCallsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
}

const ProcessCallsModal: React.FC<ProcessCallsModalProps> = ({ open, onOpenChange }) => {
  const [selectedDate, setSelectedDate] = useState<Date | undefined>();
  const [isProcessing, setIsProcessing] = useState(false);
  const { toast } = useToast();

  const handleProcessCalls = () => {
    setIsProcessing(true);

    // Prepare the request data
    const requestData: { date_override?: string; max_concurrent: number } = {
      max_concurrent: 2 // Default as requested
    };

    // If a date is selected, use it as override, otherwise let API use previous day
    if (selectedDate) {
      requestData.date_override = formatDateForTrigger(selectedDate);
    }

    // Call the new trigger API but don't wait for response
    triggerDailyCallProcessing(requestData).catch(error => {
      console.error('Background trigger error:', error);
    });

    // Show success notification immediately
    processingToast(
      "Request received",
      `Your request has been received and calls are being processed. ${selectedDate ? `Processing date: ${format(selectedDate, 'PPP')}` : 'Processing previous day'}`
    );

    // Close the modal
    onOpenChange(false);

    // Reset the form
    setSelectedDate(undefined);
    setIsProcessing(false);
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Trigger Call Processing</DialogTitle>
          <DialogDescription>
            Manually trigger the daily call processing job. Select a specific date or leave empty to process the previous day.
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <div className="space-y-4">
            <div className="space-y-2">
              <h4 className="font-medium">Processing Date (Optional)</h4>
              <p className="text-sm text-gray-600">
                If no date is selected, the system will process calls from the previous day.
              </p>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant="outline"
                    className={cn(
                      "w-full justify-start text-left font-normal",
                      !selectedDate && "text-muted-foreground"
                    )}
                    disabled={isProcessing}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {selectedDate ? format(selectedDate, "PPP") : "Select date (optional)"}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0" align="start">
                  <Calendar
                    mode="single"
                    selected={selectedDate}
                    onSelect={setSelectedDate}
                    initialFocus
                  />
                </PopoverContent>
              </Popover>
              {selectedDate && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => setSelectedDate(undefined)}
                  className="text-sm text-gray-500 hover:text-gray-700"
                >
                  Clear date selection
                </Button>
              )}
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <h5 className="font-medium text-blue-800 mb-1">Processing Settings</h5>
              <p className="text-sm text-blue-700">
                • Max concurrent tasks: 2 (default)
                <br />
                • Processing will run in the background
                <br />
                • You'll be notified when the job starts
              </p>
            </div>
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)} disabled={isProcessing}>
            Cancel
          </Button>
          <Button
            onClick={handleProcessCalls}
            disabled={isProcessing}
          >
            {isProcessing ? (
              <>
                <Loader className="mr-2 h-4 w-4 animate-spin" />
                Triggering...
              </>
            ) : (
              'Trigger Processing'
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
};

export default ProcessCallsModal;
