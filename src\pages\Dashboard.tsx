
import { useState, useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { CallData } from '@/services/mockData';
import { SentimentDistributionChart, FlagCountsChart } from '@/components/dashboard/DashboardCharts';
import CallsTable from '@/components/calls/CallsTable';
import { Loader } from 'lucide-react';
import ProcessCallsModal from '@/components/calls/ProcessCallsModal';
import { Button } from '@/components/ui/button';
import ComprehensiveFilterPanel from '@/components/dashboard/ComprehensiveFilterPanel';
import {
  fetchDashboardAnalytics,
  fetchCallListing,
  DateFilters,
  CallSpecificFilters
} from '@/services/dashboardService';
import { useToast } from '@/components/ui/use-toast';
import { errorToast } from '@/components/ui/custom-toast';

const Dashboard = () => {
  const { toast } = useToast();
  const navigate = useNavigate();
  const [dashboardData, setDashboardData] = useState<ReturnType<typeof fetchDashboardAnalytics.prototype> | null>(null);
  const [calls, setCalls] = useState<CallData[]>([]);
  const [totalCalls, setTotalCalls] = useState(0);
  const [isProcessModalOpen, setIsProcessModalOpen] = useState(false);
  const [isAnalyticsLoading, setIsAnalyticsLoading] = useState(true);
  const [isCallsLoading, setIsCallsLoading] = useState(true);
  const [paginationInfo, setPaginationInfo] = useState<{
    currentPage: number;
    totalPages: number;
    hasNext: boolean;
    hasPrevious: boolean;
    nextPage: number | null;
    previousPage: number | null;
  } | undefined>(undefined);

  // Enhanced filter state
  const [dateFilters, setDateFilters] = useState<DateFilters>({
    filter_type: 'all'
  });
  const [callFilters, setCallFilters] = useState<CallSpecificFilters>({});

  // Load dashboard analytics data with date filters
  const loadDashboardData = useCallback(async () => {
    try {
      console.log('Loading dashboard analytics with date filters:', dateFilters);
      setIsAnalyticsLoading(true);

      const data = await fetchDashboardAnalytics(dateFilters);
      console.log('Dashboard analytics data received:', data);

      if (!data || Object.keys(data).length === 0) {
        console.warn('Empty analytics data received from API, using mock data');
        const { getDashboardData } = await import('@/services/mockData');
        setDashboardData(getDashboardData());
      } else {
        setDashboardData(data);
      }
    } catch (error) {
      console.error('Failed to load dashboard analytics:', error);
      errorToast(
        'Error',
        'Failed to load dashboard analytics. Please try again later.'
      );

      // Fall back to mock data on error
      const { getDashboardData } = await import('@/services/mockData');
      setDashboardData(getDashboardData());
    } finally {
      setIsAnalyticsLoading(false);
    }
  }, [dateFilters, toast]);

  // Load call listing data with all filters
  const loadCallsData = useCallback(async (page: number = 1, perPage: number = 5) => {
    try {
      console.log('Loading calls data with filters:', { dateFilters, callFilters, page, perPage });
      setIsCallsLoading(true);

      const data = await fetchCallListing(page, perPage, dateFilters, callFilters);
      console.log('Calls data received:', data);

      if (!data || !data.calls || data.calls.length === 0) {
        console.warn('Empty calls data received from API, using mock data');
        const { mockCalls } = await import('@/services/mockData');
        const startIndex = (page - 1) * perPage;
        setCalls(mockCalls.slice(startIndex, startIndex + perPage));
        setTotalCalls(mockCalls.length);
      } else {
        setCalls(data.calls);
        setTotalCalls(data.total);

        if (data.pagination) {
          setPaginationInfo(data.pagination);
        }
      }
    } catch (error) {
      console.error('Failed to load calls data:', error);
      errorToast(
        'Error',
        'Failed to load call listings. Please try again later.'
      );

      // Fall back to mock data on error
      const { mockCalls } = await import('@/services/mockData');
      const startIndex = (page - 1) * perPage;
      setCalls(mockCalls.slice(startIndex, startIndex + perPage));
      setTotalCalls(mockCalls.length);
    } finally {
      setIsCallsLoading(false);
    }
  }, [dateFilters, callFilters, toast]);

  // Load initial data
  useEffect(() => {
    loadDashboardData();
    loadCallsData(1, 5);
  }, []);

  // Filter handlers
  const handleApplyFilters = useCallback((newDateFilters: DateFilters, newCallFilters: CallSpecificFilters) => {
    console.log('Applying filters:', { newDateFilters, newCallFilters });
    setDateFilters(newDateFilters);
    setCallFilters(newCallFilters);

    // Trigger data reload with new filters
    const loadData = async () => {
      try {
        // Load analytics with new date filters
        setIsAnalyticsLoading(true);
        const analyticsData = await fetchDashboardAnalytics(newDateFilters);
        if (analyticsData && Object.keys(analyticsData).length > 0) {
          setDashboardData(analyticsData);
        }
        setIsAnalyticsLoading(false);

        // Load calls with new filters
        setIsCallsLoading(true);
        const callsData = await fetchCallListing(1, 5, newDateFilters, newCallFilters);
        if (callsData && callsData.calls) {
          setCalls(callsData.calls);
          setTotalCalls(callsData.total);
          setPaginationInfo(callsData.pagination);
        }
        setIsCallsLoading(false);
      } catch (error) {
        console.error('Failed to apply filters:', error);
        setIsAnalyticsLoading(false);
        setIsCallsLoading(false);
      }
    };

    loadData();
  }, []);

  const handleClearAllFilters = useCallback(() => {
    console.log('Clearing all filters');
    handleApplyFilters({ filter_type: 'all' }, {});
  }, [handleApplyFilters]);

  // Loading state for the entire dashboard
  if (isAnalyticsLoading && !dashboardData) {
    return (
      <div className="container mx-auto px-4 py-8 flex flex-col items-center justify-center min-h-[60vh]">
        <Loader className="h-12 w-12 animate-spin text-iilm-primary mb-4" />
        <p className="text-lg text-gray-600">Loading dashboard data...</p>
      </div>
    );
  }

  // Force default data if dashboardData is null or empty
  if (!dashboardData ||
      !dashboardData.sentimentDistribution ||
      !dashboardData.flagCounts ||
      !dashboardData.accuracyScore ||
      !dashboardData.callVolume) {
    console.warn('Dashboard data is missing or incomplete, using default data');
    // Use mock data as fallback
    import('@/services/mockData').then(({ getDashboardData }) => {
      setDashboardData(getDashboardData());
    });
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50">
      <div className="container mx-auto px-4 py-8 animate-fade-in">
        {/* Header Section with Beautiful Gradient */}
        <div className="bg-gradient-primary rounded-2xl p-6 mb-8 shadow-beautiful text-white">
          <div className="flex justify-between items-center">
            <div>
              <h1 className="text-3xl font-bold mb-2">Dashboard Overview</h1>
              <p className="text-blue-100">Monitor and manage your counseling analytics</p>
            </div>
            <div className="flex gap-3">
              <Button
                variant="secondary"
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm shadow-lg"
                onClick={() => setIsProcessModalOpen(true)}
              >
                Process Calls
              </Button>
              <Button
                variant="secondary"
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm shadow-lg"
                onClick={() => navigate('/counselor-emails')}
              >
                Manage Emails
              </Button>
              <Button
                variant="secondary"
                className="bg-white/20 text-white border-white/30 hover:bg-white/30 backdrop-blur-sm shadow-lg"
                onClick={() => navigate('/university-info')}
              >
                University Info
              </Button>
            </div>
          </div>
        </div>

      {/* Comprehensive Filter Panel */}
      <ComprehensiveFilterPanel
        dateFilters={dateFilters}
        callFilters={callFilters}
        onApplyFilters={handleApplyFilters}
        onClearAllFilters={handleClearAllFilters}
        isLoading={isAnalyticsLoading || isCallsLoading}
        className="mb-8"
      />

        {/* Dashboard Metrics with Beautiful Cards */}
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <div className="bg-white rounded-2xl p-6 shadow-card hover:shadow-beautiful transition-all duration-300 border border-blue-100 animate-scale-in">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-blue-100 rounded-xl">
                <div className="w-6 h-6 bg-blue-500 rounded-full"></div>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-800">
                  {isAnalyticsLoading ? 'Loading...' : (dashboardData?.callVolume.total || 'N/A')}
                </p>
                <p className="text-sm text-gray-500">Total calls processed</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-700">Call Volume</h3>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-card hover:shadow-beautiful transition-all duration-300 border border-green-100 animate-scale-in">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-green-100 rounded-xl">
                <div className="w-6 h-6 bg-green-500 rounded-full"></div>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-800">
                  {isAnalyticsLoading ? 'Loading...' : (dashboardData?.callVolume.averageDuration ? `${dashboardData.callVolume.averageDuration}m` : 'N/A')}
                </p>
                <p className="text-sm text-gray-500">Per call</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-700">Average Duration</h3>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-card hover:shadow-beautiful transition-all duration-300 border border-purple-100 animate-scale-in">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-purple-100 rounded-xl">
                <div className="w-6 h-6 bg-purple-500 rounded-full"></div>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-800">
                  {isAnalyticsLoading ? 'Loading...' : (dashboardData?.accuracyScore.current !== undefined && dashboardData?.accuracyScore.current !== null && typeof dashboardData?.accuracyScore.current === 'number' ? `${Math.round(dashboardData.accuracyScore.current)}%` : 'N/A')}
                </p>
                <p className="text-sm text-gray-500">Information accuracy</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-700">Accuracy Score</h3>
          </div>

          <div className="bg-white rounded-2xl p-6 shadow-card hover:shadow-beautiful transition-all duration-300 border border-rose-100 animate-scale-in">
            <div className="flex items-center justify-between mb-4">
              <div className="p-3 bg-rose-100 rounded-xl">
                <div className="w-6 h-6 bg-rose-500 rounded-full"></div>
              </div>
              <div className="text-right">
                <p className="text-3xl font-bold text-gray-800">
                  {isAnalyticsLoading ? 'Loading...' : (dashboardData?.sentimentDistribution.find((item: {name: string, value: number}) => item.name === 'Negative')?.value !== undefined
                    ? `${Math.round(dashboardData.sentimentDistribution.find((item: {name: string, value: number}) => item.name === 'Negative')?.value || 0)}%`
                    : 'N/A')}
                </p>
                <p className="text-sm text-gray-500">Of all calls</p>
              </div>
            </div>
            <h3 className="font-semibold text-gray-700">Negative Sentiment</h3>
          </div>
        </div>

        {/* Analytics Charts with Beautiful Styling */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
          {isAnalyticsLoading ? (
            <>
              <div className="bg-white rounded-2xl p-6 shadow-card border border-gray-100">
                <div className="flex items-center justify-center h-[350px]">
                  <div className="text-center">
                    <Loader className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
                    <p className="text-gray-500">Loading sentiment data...</p>
                  </div>
                </div>
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-card border border-gray-100">
                <div className="flex items-center justify-center h-[350px]">
                  <div className="text-center">
                    <Loader className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
                    <p className="text-gray-500">Loading flag data...</p>
                  </div>
                </div>
              </div>
            </>
          ) : (
            <>
              <div className="bg-white rounded-2xl p-6 shadow-card hover:shadow-beautiful transition-all duration-300 border border-gray-100 animate-fade-in">
                <div className="flex items-center mb-4">
                  <div className="w-3 h-3 bg-blue-500 rounded-full mr-3"></div>
                  <h3 className="text-xl font-semibold text-gray-800">Sentiment Distribution</h3>
                </div>
                {dashboardData?.sentimentDistribution && (
                  <SentimentDistributionChart data={dashboardData.sentimentDistribution} />
                )}
              </div>
              <div className="bg-white rounded-2xl p-6 shadow-card hover:shadow-beautiful transition-all duration-300 border border-gray-100 animate-fade-in">
                <div className="flex items-center mb-4">
                  <div className="w-3 h-3 bg-purple-500 rounded-full mr-3"></div>
                  <h3 className="text-xl font-semibold text-gray-800">Conversation Flags</h3>
                </div>
                {dashboardData?.flagCounts && (
                  <FlagCountsChart data={dashboardData.flagCounts} />
                )}
              </div>
            </>
          )}
        </div>

        {/* Call Listings Section with Beautiful Styling */}
        <div className="bg-white rounded-2xl p-6 shadow-card border border-gray-100 mb-8 animate-fade-in">
          <div className="flex items-center mb-6">
            <div className="w-4 h-4 bg-indigo-500 rounded-full mr-3"></div>
            <h2 className="text-2xl font-bold text-gray-800">Recent Call Listings</h2>
          </div>
          {isCallsLoading ? (
            <div className="flex flex-col items-center justify-center py-16">
              <div className="text-center">
                <Loader className="h-12 w-12 animate-spin text-blue-500 mx-auto mb-4" />
                <p className="text-gray-500 text-lg">Loading call data...</p>
                <p className="text-gray-400 text-sm mt-2">Please wait while we fetch the latest calls</p>
              </div>
            </div>
          ) : (
            <CallsTable
              calls={calls}
              totalCalls={totalCalls}
              pagination={paginationInfo}
              onPageChange={async (skip, limit) => {
                // Convert skip/limit to page/perPage
                const page = Math.floor(skip / limit) + 1;
                const perPage = limit;

                console.log(`Dashboard onPageChange called with skip=${skip}, limit=${limit}`);
                console.log(`Converted to page=${page}, perPage=${perPage}`);

                await loadCallsData(page, perPage);
              }}
            />
          )}
        </div>

        <ProcessCallsModal
          open={isProcessModalOpen}
          onOpenChange={setIsProcessModalOpen}
        />
      </div>
    </div>
  );
};

export default Dashboard;
