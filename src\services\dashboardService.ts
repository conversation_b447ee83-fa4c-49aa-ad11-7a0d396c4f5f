import { httpClient } from './httpClient';
import { getDashboardData, mockCalls, CallData } from './mockData';

/**
 * Safely parses accuracy percentage from various formats
 * @param value The accuracy value from API (can be number, string, or null)
 * @returns Parsed number or null if invalid/string
 */
const parseAccuracyPercentage = (value: number | string | null | undefined): number | null => {
  if (value === null || value === undefined) {
    return null;
  }

  if (typeof value === 'number') {
    return isNaN(value) ? null : value;
  }

  if (typeof value === 'string') {
    // Handle string values that might be valid numbers
    const trimmedValue = value.trim().toLowerCase();

    // Check for explicit N/A values
    if (trimmedValue === 'na' || trimmedValue === 'n/a' || trimmedValue === 'not available' || trimmedValue === '') {
      return null;
    }

    // Try to parse as a number
    const numericValue = parseFloat(value);
    if (!isNaN(numericValue)) {
      return numericValue;
    }

    // If it's not a valid number, treat as N/A
    return null;
  }

  return null;
};

// New filter interfaces for enhanced filtering
export interface DateFilters {
  filter_type: 'all' | 'today' | 'yesterday' | 'specific_day' | 'date_range';
  specific_date?: string; // YYYY-MM-DD format
  start_date?: string; // YYYY-MM-DD format
  end_date?: string; // YYYY-MM-DD format
}

export interface CallSpecificFilters {
  sentiment_filter?: 'positive' | 'neutral' | 'negative';
  conversation_flags?: 'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive';
  agent_name?: string;
}

// Enhanced analytics response interface
export interface CallAnalytics {
  total_calls: number;
  average_duration: number;
  average_accuracy: number | string | null; // Support string values like "N/A"
  positive_sentiment_percentage: number;
  sentiment_distribution: {
    positive_count: number;
    negative_count: number;
    neutral_count: number;
    positive_percentage: number;
    negative_percentage: number;
    neutral_percentage: number;
  };
  emotional_flags: {
    frustration: number;
    confusion: number;
    urgency: number;
    satisfaction: number;
    abusive?: number;
  };
  conversation_tones?: {
    [key: string]: number;
  };
  generated_at: string;
}

// Enhanced call data detail interface
export interface CallDataDetail {
  id: string;
  call_id: string;
  call_duration: number;
  audio_url: string | null;
  processed: boolean;
  created_at: string;
  transcription_id: string | null;
  agent_name: string | null;
  call_date: string | null;
  call_time: string | null;
  group_name: string | null;
  student_id: string | null;
  student_name: string | null;
  call_direction: string | null;
  call_source: string | null;
  relevant_category?: string | null;
  transcription?: {
    id?: string;
    full_text?: string;
    text_preview?: string;
    duration_seconds?: number;
    created_at?: string;
    speaker_segments?: Array<{
      speaker: string;
      text: string;
    }>;
    speaker_count?: number;
    sentiment_info?: {
      overall_sentiment: 'positive' | 'neutral' | 'negative';
      sentiment_score: number;
      is_student_university_conversation?: boolean;
      conversation_topic?: string;
      summary?: string;
      key_points?: string[];
      student_sentiment?: string;
      assistant_sentiment?: string;
      speaker_roles?: Record<string, string>;
      action_items?: string[];
      relevant_category?: string;
    };
    accuracy_info?: {
      overall_assessment?: string;
      missed_information?: string[];
      incorrect_information?: Array<{
        provided: string;
        correct: string;
      }>;
      incomplete_information?: Array<{
        provided: string;
        complete: string;
      }>;
      correct_information?: string[];
      accuracy_percentage?: number | string | null;
      conversation_flags?: ('frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive')[] | {
        satisfaction?: boolean;
        confusion?: boolean;
        urgency?: boolean;
        frustration?: boolean;
        abusive?: boolean;
        [key: string]: boolean | undefined;
      };
      conversation_tone?: string;
      created_at?: string;
    };
    metadata?: {
      duration_seconds?: number;
      channels?: number;
    };
  };
}

// Paginated response interface
export interface PaginatedResponse<T> {
  items: T[];
  meta: {
    current_page: number;
    page_count: number;
    per_page: number;
    total_count: number;
    has_next: boolean;
    has_previous: boolean;
    next_page: number | null;
    previous_page: number | null;
  };
}

// Utility functions for filter handling
export const buildDateQueryParams = (dateFilters: DateFilters): Record<string, string> => {
  const params: Record<string, string> = {};

  if (dateFilters.filter_type !== 'all') {
    params.filter_type = dateFilters.filter_type;

    if (dateFilters.filter_type === 'specific_day' && dateFilters.specific_date) {
      params.specific_date = dateFilters.specific_date;
    } else if (dateFilters.filter_type === 'date_range' && dateFilters.start_date && dateFilters.end_date) {
      params.start_date = dateFilters.start_date;
      params.end_date = dateFilters.end_date;
    }
  }

  return params;
};

export const buildCallQueryParams = (
  dateFilters: DateFilters,
  callFilters: CallSpecificFilters,
  page: number = 1,
  perPage: number = 5
): Record<string, string> => {
  const params: Record<string, string> = {
    page: page.toString(),
    per_page: perPage.toString(),
    ...buildDateQueryParams(dateFilters)
  };

  if (callFilters.sentiment_filter) {
    params.sentiment_filter = callFilters.sentiment_filter;
  }

  if (callFilters.conversation_flags) {
    params.conversation_flags = callFilters.conversation_flags;
  }

  if (callFilters.agent_name && callFilters.agent_name.trim()) {
    params.agent_name = callFilters.agent_name.trim();
  }

  return params;
};

// Transform CallDataDetail to CallData
export const transformCallDataDetail = (detail: CallDataDetail): CallData => {
  // Format duration from seconds to mm:ss
  const formatDuration = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };

  // Format date and time
  let dateTime = '';
  if (detail.call_date && detail.call_time) {
    dateTime = `${detail.call_date}T${detail.call_time}`;
  } else if (detail.created_at) {
    dateTime = detail.created_at;
  }

  // Extract sentiment
  let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
  if (detail.transcription?.sentiment_info?.overall_sentiment) {
    sentiment = detail.transcription.sentiment_info.overall_sentiment;
  }

  // Extract flags
  let flags: ('frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive')[] = [];
  if (detail.transcription?.accuracy_info?.conversation_flags) {
    const flagsData = detail.transcription.accuracy_info.conversation_flags;
    if (Array.isArray(flagsData)) {
      flags = flagsData.filter(flag => flag) as ('frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive')[];
    } else if (typeof flagsData === 'object') {
      flags = Object.entries(flagsData)
        .filter(([key, value]) => value === true)
        .map(([key]) => key) as ('frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive')[];
    }
  }

  // Extract accuracy using the safe parser
  const infoAccuracy = parseAccuracyPercentage(detail.transcription?.accuracy_info?.accuracy_percentage);

  return {
    id: detail.id || detail.call_id || '',
    dateTime,
    counselor: detail.agent_name || 'N/A',
    studentNumber: detail.student_name || detail.student_id || 'N/A',
    duration: formatDuration(detail.call_duration || 0),
    sentiment,
    flags,
    infoAccuracy,
    transcript: detail.transcription?.full_text || '',
    summary: detail.transcription?.sentiment_info?.summary || '',
    missedInfo: detail.transcription?.accuracy_info?.missed_information || [],
    audioUrl: detail.audio_url || '',
    transcriptionId: detail.transcription_id || '',
    callDirection: detail.call_direction || '',
    callSource: detail.call_source || '',
    groupName: detail.group_name || '',
    sentimentScore: detail.transcription?.sentiment_info?.sentiment_score || 0,
    conversationTopic: detail.transcription?.sentiment_info?.conversation_topic || '',
    relevantCategory: detail.transcription?.sentiment_info?.relevant_category || '',
    conversationTone: detail.transcription?.accuracy_info?.conversation_tone || '',
    isStudentUniversityConversation: detail.transcription?.sentiment_info?.is_student_university_conversation || false,
    sentimentSummary: detail.transcription?.sentiment_info?.summary || '',
    keyPoints: detail.transcription?.sentiment_info?.key_points || [],
    studentSentiment: detail.transcription?.sentiment_info?.student_sentiment || '',
    assistantSentiment: detail.transcription?.sentiment_info?.assistant_sentiment || '',
    speakerRoles: detail.transcription?.sentiment_info?.speaker_roles || {},
    actionItems: detail.transcription?.sentiment_info?.action_items || [],
    incorrectInfo: detail.transcription?.accuracy_info?.incorrect_information || [],
    segments: detail.transcription?.speaker_segments || []
  };
};

// Transform analytics response
export const transformAnalyticsResponse = (analytics: CallAnalytics): ReturnType<typeof getDashboardData> => {
  // Parse accuracy percentage safely
  const parsedAccuracy = parseAccuracyPercentage(analytics.average_accuracy);

  return {
    callVolume: {
      total: analytics.total_calls,
      averageDuration: Math.round(analytics.average_duration / 60) // Convert seconds to minutes
    },
    accuracyScore: {
      current: parsedAccuracy,
      previous: parsedAccuracy ? parsedAccuracy - 5 : null // Mock previous value for trend
    },
    sentimentDistribution: [
      { name: 'Positive', value: analytics.sentiment_distribution.positive_percentage },
      { name: 'Neutral', value: analytics.sentiment_distribution.neutral_percentage },
      { name: 'Negative', value: analytics.sentiment_distribution.negative_percentage }
    ],
    flagCounts: [
      { name: 'Frustration', count: analytics.emotional_flags.frustration },
      { name: 'Confusion', count: analytics.emotional_flags.confusion },
      { name: 'Urgency', count: analytics.emotional_flags.urgency },
      { name: 'Satisfaction', count: analytics.emotional_flags.satisfaction },
      { name: 'Abusive', count: analytics.emotional_flags.abusive || 0 }
    ]
  };
};

// Define interfaces for API responses
export interface DashboardAnalyticsResponse {
  // Fields from the actual API response
  total_calls?: number;
  average_duration?: number; // Format: seconds
  average_accuracy?: number | string | null; // Support string values like "N/A"
  positive_sentiment_percentage?: number;
  sentiment_distribution?: {
    positive_count?: number;
    negative_count?: number;
    neutral_count?: number;
    positive_percentage?: number;
    negative_percentage?: number;
    neutral_percentage?: number;
  };
  emotional_flags?: {
    frustration?: number;
    confusion?: number;
    urgency?: number;
    satisfaction?: number;
  };
  generated_at?: string;

  // Legacy fields for backward compatibility
  accuracy_score?: {
    current?: number;
    previous?: number;
  };
  flag_counts?: {
    frustration?: number;
    confusion?: number;
    urgency?: number;
    satisfaction?: number;
  };
  sentiment_trend?: Array<{
    date?: string;
    score?: number;
  }>;

  // Add fallback properties for different API response formats
  callVolume?: number;
  totalCalls?: number;
  averageDuration?: string;
  accuracyScore?: number | {
    current?: number;
    previous?: number;
  };
  sentimentDistribution?: {
    [key: string]: number;
  } | Array<{
    name?: string;
    value?: number;
  }>;
  flagCounts?: {
    [key: string]: number;
  } | Array<{
    name?: string;
    count?: number;
  }>;
}

// Interface for pagination metadata
export interface PaginationMeta {
  total_count: number;
  page_count: number;
  current_page: number;
  per_page: number;
  has_next: boolean;
  has_previous: boolean;
  next_page: number | null;
  previous_page: number | null;
}

// Interface for a single call item
export interface CallItem {
  id?: string;
  call_id: string;
  call_duration?: number; // Duration in seconds
  audio_url?: string;
  processed?: boolean;
  created_at: string;
  transcription_id?: string;
  agent_name?: string;
  call_date?: string;
  call_time?: string;
  group_name?: string;
  student_id?: string | null;
  student_name?: string | null;
  call_direction?: string;
  call_source?: string;
  transcription?: {
    sentiment_info?: {
      overall_sentiment?: 'positive' | 'neutral' | 'negative';
      sentiment_score?: number;
    };
    accuracy_info?: {
      accuracy_percentage?: number | string | null;
      // Support both array of strings and object with boolean values
      conversation_flags?: string[] | {
        frustration?: boolean;
        confusion?: boolean;
        urgency?: boolean;
        satisfaction?: boolean;
        [key: string]: boolean | undefined;
      };
    };
  };
  // Legacy fields for backward compatibility
  duration?: string;
  counselor_name?: string;
  student_number?: string;
  sentiment?: 'positive' | 'neutral' | 'negative';
  flags?: string[];
  info_accuracy?: number;
}

// Updated response interface with new structure
export interface CallListingResponse {
  items: CallItem[];
  meta: PaginationMeta;

  // Legacy fields for backward compatibility
  total?: number;
  calls?: CallItem[];
}

// Transform API response to match the format expected by the UI
export const transformAnalyticsData = (data: DashboardAnalyticsResponse) => {
  console.log('Starting data transformation for analytics data');
  console.log('Raw API response data:', data);

  // Handle sentiment distribution based on different possible formats
  let sentimentDistribution: Array<{name: string, value: number}>;

  if (Array.isArray(data?.sentimentDistribution)) {
    // If API directly returns an array format
    console.log('Using sentimentDistribution array from API');
    sentimentDistribution = data.sentimentDistribution.map(item => ({
      name: item.name || 'Unknown',
      value: item.value || 0
    }));
  } else if (data?.sentiment_distribution) {
    // If API returns the new format with percentages
    console.log('Using sentiment_distribution object from API with percentages');
    sentimentDistribution = [
      { name: 'Positive', value: data.sentiment_distribution.positive_percentage || 0 },
      { name: 'Neutral', value: data.sentiment_distribution.neutral_percentage || 0 },
      { name: 'Negative', value: data.sentiment_distribution.negative_percentage || 0 }
    ];
  } else if (data?.positive_sentiment_percentage !== undefined) {
    // If API returns positive_sentiment_percentage directly
    console.log('Using positive_sentiment_percentage from API');
    // Calculate other percentages based on the positive percentage
    // This is an approximation since we don't have exact values for neutral/negative
    const positivePercentage = data.positive_sentiment_percentage;
    // Assuming the rest is split evenly between neutral and negative
    const remainingPercentage = 100 - positivePercentage;
    const neutralPercentage = remainingPercentage * 0.7; // 70% of remaining as neutral
    const negativePercentage = remainingPercentage * 0.3; // 30% of remaining as negative

    sentimentDistribution = [
      { name: 'Positive', value: positivePercentage },
      { name: 'Neutral', value: neutralPercentage },
      { name: 'Negative', value: negativePercentage }
    ];
  } else if (typeof data?.sentimentDistribution === 'object' && !Array.isArray(data?.sentimentDistribution)) {
    // If API returns a different object format
    console.log('Converting sentimentDistribution object to array');
    sentimentDistribution = Object.entries(data.sentimentDistribution as Record<string, number>).map(([key, value]) => ({
      name: key.charAt(0).toUpperCase() + key.slice(1),
      value: value || 0
    }));
  } else {
    // Fallback to empty data
    console.log('No sentiment distribution data found, using defaults');
    sentimentDistribution = [
      { name: 'Positive', value: 33 },
      { name: 'Neutral', value: 33 },
      { name: 'Negative', value: 34 }
    ];
  }
  console.log('Transformed sentiment distribution:', sentimentDistribution);

  // Handle flag counts based on different possible formats
  let flagCounts: Array<{name: string, count: number}>;

  if (Array.isArray(data?.flagCounts)) {
    // If API directly returns an array format
    console.log('Using flagCounts array from API');
    flagCounts = data.flagCounts.map(item => ({
      name: item.name || 'Unknown',
      count: item.count || 0
    }));
  } else if (data?.emotional_flags) {
    // If API returns the new emotional_flags format
    console.log('Using emotional_flags object from API');
    flagCounts = [
      { name: 'Frustration', count: data.emotional_flags.frustration || 0 },
      { name: 'Confusion', count: data.emotional_flags.confusion || 0 },
      { name: 'Urgency', count: data.emotional_flags.urgency || 0 },
      { name: 'Satisfaction', count: data.emotional_flags.satisfaction || 0 }
    ];
  } else if (data?.flag_counts) {
    // If API returns the legacy flag_counts format
    console.log('Using flag_counts object from API');
    flagCounts = [
      { name: 'Frustration', count: data.flag_counts.frustration || 0 },
      { name: 'Confusion', count: data.flag_counts.confusion || 0 },
      { name: 'Urgency', count: data.flag_counts.urgency || 0 },
      { name: 'Satisfaction', count: data.flag_counts.satisfaction || 0 }
    ];
  } else if (typeof data?.flagCounts === 'object' && !Array.isArray(data?.flagCounts)) {
    // If API returns a different object format
    console.log('Converting flagCounts object to array');
    flagCounts = Object.entries(data.flagCounts as Record<string, number>).map(([key, value]) => ({
      name: key.charAt(0).toUpperCase() + key.slice(1),
      count: value || 0
    }));
  } else {
    // Fallback to empty data
    console.log('No flag counts data found, using defaults');
    flagCounts = [
      { name: 'Frustration', count: 4 },
      { name: 'Confusion', count: 3 },
      { name: 'Urgency', count: 2 },
      { name: 'Satisfaction', count: 4 }
    ];
  }
  console.log('Transformed flag counts:', flagCounts);

  // Handle accuracy score
  let accuracyScore = {
    current: 0,
    previous: 0
  };

  if (data?.average_accuracy !== undefined) {
    console.log('Using average_accuracy from API');
    // Parse accuracy percentage safely
    const parsedAccuracy = parseAccuracyPercentage(data.average_accuracy);
    // Use the current average_accuracy and create a previous value that's slightly lower
    // This is just for display purposes to show a trend
    accuracyScore = {
      current: parsedAccuracy,
      previous: parsedAccuracy ? parsedAccuracy * 0.95 : null // 5% lower for previous
    };
  } else if (data?.accuracy_score) {
    console.log('Using accuracy_score object from API');
    accuracyScore = {
      current: data.accuracy_score.current || 0,
      previous: data.accuracy_score.previous || 0
    };
  } else if (typeof data?.accuracyScore === 'number') {
    console.log('Using accuracyScore number from API');
    accuracyScore = {
      current: data.accuracyScore,
      previous: data.accuracyScore - 3 // Arbitrary difference for trend
    };
  } else if (typeof data?.accuracyScore === 'object') {
    console.log('Using accuracyScore object from API');
    accuracyScore = {
      current: data.accuracyScore.current || 0,
      previous: data.accuracyScore.previous || 0
    };
  }
  console.log('Transformed accuracy score:', accuracyScore);

  // Handle call volume
  let callVolume = {
    total: 0,
    averageDuration: 'N/A'
  };

  if (data?.total_calls !== undefined) {
    console.log('Using total_calls and average_duration from API');

    // Convert average_duration from seconds to minutes:seconds format
    let formattedDuration = 'N/A';
    if (data.average_duration !== undefined) {
      const minutes = Math.floor(data.average_duration / 60);
      const seconds = Math.floor(data.average_duration % 60);
      formattedDuration = `${minutes}.${seconds < 10 ? '0' + seconds : seconds}`;
    }

    callVolume = {
      total: data.total_calls,
      averageDuration: formattedDuration
    };
  } else if (data?.totalCalls !== undefined) {
    console.log('Using totalCalls from API');
    callVolume = {
      total: data.totalCalls,
      averageDuration: data.averageDuration || 'N/A'
    };
  } else if (data?.callVolume !== undefined) {
    console.log('Using callVolume from API');
    if (typeof data.callVolume === 'number') {
      callVolume = {
        total: data.callVolume,
        averageDuration: data.averageDuration || 'N/A'
      };
    } else if (typeof data.callVolume === 'object') {
      // Type assertion to avoid 'never' type error
      const callVolumeObj = data.callVolume as {
        total?: number;
        averageDuration?: string;
      };

      callVolume = {
        total: callVolumeObj.total || 0,
        averageDuration: callVolumeObj.averageDuration || 'N/A'
      };
    }
  }
  console.log('Transformed call volume:', callVolume);

  // Create the final transformed data structure
  const transformedData = {
    sentimentDistribution,
    flagCounts,
    accuracyScore,
    callVolume,
    sentimentTrend: data?.sentiment_trend || []
  };

  console.log('Final transformed data:', transformedData);
  return transformedData;
};

// Transform API call listing to match the format expected by the UI
export const transformCallsData = (data: CallListingResponse): CallData[] => {
  console.log('Starting data transformation for call listing data');

  // Determine which array of calls to use based on the response structure
  let callsArray: CallItem[] = [];

  if (data.items && Array.isArray(data.items)) {
    console.log('Using items array from new API response format');
    callsArray = data.items;
  } else if (data.calls && Array.isArray(data.calls)) {
    console.log('Using calls array from legacy API response format');
    callsArray = data.calls;
  } else {
    console.error('Invalid data format for transformCallsData - no valid calls array found:', data);
    return [];
  }

  return callsArray.map(call => {
    // Skip processing if call is null or not an object
    if (!call || typeof call !== 'object') {
      console.error('Invalid call object:', call);
      return null;
    }

    console.log(`Transforming call ${call.call_id || 'unknown'}:`, call);

    // Extract sentiment from the nested structure or use legacy field
    let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
    if (call.transcription?.sentiment_info?.overall_sentiment) {
      sentiment = call.transcription.sentiment_info.overall_sentiment;
    } else if (call.sentiment) {
      sentiment = call.sentiment;
    }

    // Extract flags from the nested structure or use legacy field
    let flags: string[] = [];

    // Handle the case where conversation_flags is an object with boolean values
    if (call.transcription?.accuracy_info?.conversation_flags) {
      const conversationFlags = call.transcription.accuracy_info.conversation_flags;

      // Check if it's an object with boolean values
      if (typeof conversationFlags === 'object' && !Array.isArray(conversationFlags)) {
        console.log('Conversation flags is an object:', conversationFlags);
        // Convert object with boolean values to array of strings for true values
        flags = Object.entries(conversationFlags)
          .filter(([_, value]) => value === true)
          .map(([key, _]) => key);
      } else if (Array.isArray(conversationFlags)) {
        // If it's already an array, use it directly
        flags = conversationFlags;
      }
    } else if (call.flags) {
      flags = call.flags;
    }

    // Ensure flags is an array
    if (!Array.isArray(flags)) {
      flags = [];
    }

    console.log(`Extracted flags for call ${call.call_id || 'unknown'}:`, flags);

    // Filter and validate flags to ensure they match the expected types
    const validFlagTypes = ['frustration', 'confusion', 'urgency', 'satisfaction', 'abusive'];
    const validatedFlags = flags
      .filter(flag => typeof flag === 'string' && validFlagTypes.includes(flag.toLowerCase()))
      .map(flag => flag.toLowerCase()) as Array<'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive'>;

    console.log(`Validated flags for call ${call.call_id || 'unknown'}:`, validatedFlags);

    // Format duration from seconds to mm:ss format
    let formattedDuration = '00:00';
    if (call.call_duration !== undefined && call.call_duration !== null) {
      const minutes = Math.floor(call.call_duration / 60);
      const seconds = Math.floor(call.call_duration % 60);
      formattedDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
    } else if (call.duration) {
      formattedDuration = call.duration;
    }

    // Format date and time
    let dateTime = call.created_at;
    if (call.call_date && call.call_time) {
      // If both call_date and call_time are available, combine them
      dateTime = `${call.call_date}T${call.call_time}`;
    }

    // Extract accuracy from the nested structure or use legacy field
    let infoAccuracy: number | null = null;
    if (call.transcription?.accuracy_info?.accuracy_percentage !== undefined) {
      infoAccuracy = parseAccuracyPercentage(call.transcription.accuracy_info.accuracy_percentage);
    } else if (call.info_accuracy !== undefined) {
      infoAccuracy = parseAccuracyPercentage(call.info_accuracy);
    }

    // Round accuracy to 2 decimal places if it's a valid number
    if (infoAccuracy !== null) {
      infoAccuracy = Math.round(infoAccuracy * 100) / 100;
    }

    return {
      id: call.id || call.call_id || 'N/A',
      dateTime: dateTime || new Date().toISOString(),
      counselor: call.agent_name || call.counselor_name || 'N/A',
      studentNumber: call.student_name || call.student_number || 'N/A',
      duration: formattedDuration,
      sentiment: sentiment,
      flags: validatedFlags,
      infoAccuracy: infoAccuracy,
      // Additional fields that might be useful for the call details page
      audioUrl: call.audio_url,
      transcriptionId: call.transcription_id,
      callDirection: call.call_direction,
      callSource: call.call_source,
      groupName: call.group_name,
      sentimentScore: call.transcription?.sentiment_info?.sentiment_score
    };
  }).filter(Boolean) as CallData[]; // Filter out any null values from invalid call objects
};

/**
 * Fetches dashboard analytics data from the API with date filters
 * @param dateFilters Date filter parameters
 * @returns Promise with dashboard analytics data
 */
export const fetchDashboardAnalytics = async (dateFilters?: DateFilters): Promise<ReturnType<typeof transformAnalyticsData>> => {
  try {
    console.log('Fetching dashboard analytics from API...');

    // Build query parameters
    const queryParams = dateFilters ? buildDateQueryParams(dateFilters) : {};
    const queryString = new URLSearchParams(queryParams).toString();
    const url = `/api/v1/transcription/analytics${queryString ? `?${queryString}` : ''}`;

    console.log('Analytics API URL:', url);
    const response = await httpClient.get(url);
    console.log('Dashboard analytics API response:', response);

    // Check if response has the expected structure
    if (!response || typeof response !== 'object') {
      console.error('Invalid API response format:', response);
      return getDashboardData();
    }

    // Try to use new CallAnalytics format first
    if ('total_calls' in response && 'sentiment_distribution' in response && 'emotional_flags' in response) {
      console.log('Using new CallAnalytics response format');
      const analyticsData = response as CallAnalytics;
      return transformAnalyticsResponse(analyticsData);
    }

    const transformedData = transformAnalyticsData(response as DashboardAnalyticsResponse);
    console.log('Transformed dashboard data:', transformedData);
    return transformedData;
  } catch (error) {
    console.error('Error fetching dashboard analytics:', error);
    // Fallback to mock data if API fails
    return getDashboardData();
  }
};

/**
 * Fetches call listing data from the API with comprehensive filtering
 * @param page Page number (1-based) for pagination
 * @param perPage Number of records per page
 * @param dateFilters Date filter parameters
 * @param callFilters Call-specific filter parameters
 * @returns Promise with call listing data and pagination information
 */
export const fetchCallListing = async (
  page = 1,
  perPage = 5,
  dateFilters?: DateFilters,
  callFilters?: CallSpecificFilters
): Promise<{
  calls: CallData[],
  total: number,
  pagination?: {
    currentPage: number,
    totalPages: number,
    hasNext: boolean,
    hasPrevious: boolean,
    nextPage: number | null,
    previousPage: number | null
  }
}> => {
  console.log(`fetchCallListing called with page=${page}, perPage=${perPage}, dateFilters:`, dateFilters, 'callFilters:', callFilters);

  // Ensure page is at least 1 (API requires page >= 1)
  const validPage = Math.max(1, page);
  // Ensure perPage is at least 1
  const validPerPage = Math.max(1, perPage);

  console.log(`Using validPage=${validPage}, validPerPage=${validPerPage}`);

  try {
    // Build query parameters with all filters
    const queryParams = buildCallQueryParams(
      dateFilters || { filter_type: 'all' },
      callFilters || {},
      validPage,
      validPerPage
    );

    const queryString = new URLSearchParams(queryParams).toString();
    const url = `/api/v1/transcription/calls?${queryString}`;

    console.log(`Fetching call listing from API with URL: ${url}`);
    const response = await httpClient.get(url);
    console.log('Call listing API response status:', response ? 'Success' : 'Empty');

    // Handle case where response is null or undefined
    if (!response) {
      console.error('Empty API response');
      console.warn('Falling back to mock data due to empty API response');
      const mockStart = (validPage - 1) * validPerPage;
      return {
        calls: mockCalls.slice(mockStart, mockStart + validPerPage),
        total: mockCalls.length
      };
    }

    // Handle case where API returns an array directly (without the expected wrapper object)
    if (Array.isArray(response)) {
      console.log('API returned an array directly instead of a wrapped object');
      console.log('Sample call from array:', response.length > 0 ? {
        id: response[0].id,
        call_id: response[0].call_id,
        hasTranscription: !!response[0].transcription
      } : 'No calls');

      // Log a sample of the array response
      if (response.length > 0) {
        console.log('Sample call from array response:', {
          id: response[0].id,
          call_id: response[0].call_id,
          call_duration: response[0].call_duration,
          transcription: response[0].transcription ? {
            has_sentiment_info: !!response[0].transcription.sentiment_info,
            has_accuracy_info: !!response[0].transcription.accuracy_info,
            conversation_flags_type: response[0].transcription.accuracy_info?.conversation_flags ?
              (Array.isArray(response[0].transcription.accuracy_info.conversation_flags) ?
                'array' : typeof response[0].transcription.accuracy_info.conversation_flags) : 'none'
          } : 'none'
        });
      }

      // Create a CallListingResponse object from the array
      const adaptedResponse: CallListingResponse = {
        items: response,
        meta: {
          total_count: response.length,
          page_count: 1,
          current_page: 1,
          per_page: response.length,
          has_next: false,
          has_previous: false,
          next_page: null,
          previous_page: null
        }
      };

      const transformedCalls = transformCallsData(adaptedResponse);
      console.log(`Transformed ${transformedCalls.length} calls from array response successfully`);

      return {
        calls: transformedCalls,
        total: response.length,
        pagination: {
          currentPage: 1,
          totalPages: 1,
          hasNext: false,
          hasPrevious: false,
          nextPage: null,
          previousPage: null
        }
      };
    }

    // Handle case where response is an object (the expected format)
    if (typeof response === 'object') {
      // Log the response structure to help with debugging
      console.log('Call listing API response structure:', {
        hasItems: 'items' in response,
        hasMeta: 'meta' in response,
        // Legacy format
        hasTotal: 'total' in response,
        hasCalls: 'calls' in response
      });

      // Check for new API format with items and meta (PaginatedResponse)
      if ('items' in response && 'meta' in response) {
        console.log('Using new PaginatedResponse format with items and meta');

        const data = response as PaginatedResponse<CallDataDetail>;

        // Transform CallDataDetail items to CallData
        const transformedCalls = data.items.map(item => transformCallDataDetail(item));
        console.log(`Transformed ${transformedCalls.length} calls successfully from new PaginatedResponse format`);

        // Extract pagination information from meta
        console.log('Raw meta data from API:', data.meta);

        const pagination = {
          currentPage: data.meta.current_page,
          totalPages: data.meta.page_count,
          hasNext: data.meta.has_next,
          hasPrevious: data.meta.has_previous,
          nextPage: data.meta.next_page,
          previousPage: data.meta.previous_page
        };

        console.log('Extracted pagination information:', pagination);

        return {
          calls: transformedCalls,
          total: data.meta.total_count,
          pagination
        };
      }

      // Check for legacy API format with items and meta (CallListingResponse)
      if ('items' in response && 'meta' in response && 'total_count' in (response as any).meta) {
        console.log('Using legacy CallListingResponse format with items and meta');

        const data = response as CallListingResponse;
        const transformedCalls = transformCallsData(data);
        console.log(`Transformed ${transformedCalls.length} calls successfully from legacy format`);

        // Extract pagination information from meta
        console.log('Raw meta data from API:', data.meta);

        const pagination = {
          currentPage: data.meta.current_page,
          totalPages: data.meta.page_count,
          hasNext: data.meta.has_next,
          hasPrevious: data.meta.has_previous,
          nextPage: data.meta.next_page,
          previousPage: data.meta.previous_page
        };

        console.log('Extracted pagination information:', pagination);

        return {
          calls: transformedCalls,
          total: data.meta.total_count,
          pagination
        };
      }

      // Check for legacy format with calls property
      if ('calls' in response) {
        console.log('Using legacy API response format with calls property');

        // Check if calls array is empty
        if (!Array.isArray(response.calls) || response.calls.length === 0) {
          console.warn('API returned empty calls array');
          if (validPage > 1) {
            console.warn('Pagination may have exceeded available data, returning empty result');
            return {
              calls: [],
              total: response.total || 0
            };
          } else {
            console.warn('Falling back to mock data due to empty API response');
            return {
              calls: mockCalls.slice(0, validPerPage),
              total: mockCalls.length
            };
          }
        }

        // Create a compatible response format
        const adaptedResponse: CallListingResponse = {
          items: response.calls,
          meta: {
            total_count: response.total || response.calls.length,
            page_count: Math.ceil((response.total || response.calls.length) / validPerPage),
            current_page: validPage,
            per_page: validPerPage,
            has_next: validPage * validPerPage < (response.total || response.calls.length),
            has_previous: validPage > 1,
            next_page: validPage * validPerPage < (response.total || response.calls.length) ? validPage + 1 : null,
            previous_page: validPage > 1 ? validPage - 1 : null
          }
        };

        const transformedCalls = transformCallsData(adaptedResponse);
        console.log(`Transformed ${transformedCalls.length} calls successfully from legacy format`);

        return {
          calls: transformedCalls,
          total: response.total || transformedCalls.length
        };
      }

      // If we get here, the response is in an unexpected object format
      console.error('Invalid API response format (missing expected properties):', response);
      console.warn('Falling back to mock data due to invalid API response format');
      const mockStart = (validPage - 1) * validPerPage;
      return {
        calls: mockCalls.slice(mockStart, mockStart + validPerPage),
        total: mockCalls.length
      };
    }

    // If we get here, the response is in an unexpected format
    console.error('Unexpected API response format:', typeof response);
    console.warn('Falling back to mock data due to unexpected API response format');
    const mockStart = (validPage - 1) * validPerPage;
    return {
      calls: mockCalls.slice(mockStart, mockStart + validPerPage),
      total: mockCalls.length
    };
  } catch (error) {
    console.error('Error fetching call listing:', error);

    // Log more details about the error
    if (error instanceof Error) {
      console.error('Error name:', error.name);
      console.error('Error message:', error.message);
      console.error('Error stack:', error.stack);
    }

    // Fallback to mock data if API fails
    console.warn('Falling back to mock data due to API error');
    const mockStart = (validPage - 1) * validPerPage;
    return {
      calls: mockCalls.slice(mockStart, mockStart + validPerPage),
      total: mockCalls.length
    };
  }
};
