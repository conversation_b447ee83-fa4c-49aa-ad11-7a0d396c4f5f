
// Service with mock data for development

export interface CallData {
  id: string;
  dateTime: string;
  counselor: string;
  studentNumber: string;
  duration: string; // Format: "mm:ss"
  sentiment: 'positive' | 'neutral' | 'negative';
  flags: Array<'frustration' | 'confusion' | 'urgency' | 'satisfaction' | 'abusive'>;
  infoAccuracy: number | null | undefined; // Percentage
  transcript?: string;
  summary?: string;
  missedInfo?: string[];
  incompleteInfo?: Array<{
    provided: string;
    complete: string;
  }>;
  // Additional fields from the API
  audioUrl?: string;
  transcriptionId?: string;
  callDirection?: string;
  callSource?: string;
  groupName?: string;
  sentimentScore?: number;
  // Additional fields for detailed view
  segments?: Array<{
    speaker: string;
    text: string;
    start_time?: number;
    end_time?: number;
  }>;
  // Complex incorrect information structure
  incorrectInfo?: Array<{
    provided: string;
    correct: string;
  }> | string[];
  // Additional sentiment info
  conversationTopic?: string;
  relevantCategory?: string;
  conversationTone?: string;
  isStudentUniversityConversation?: boolean;
  sentimentSummary?: string;
  keyPoints?: string[];
  studentSentiment?: string;
  assistantSentiment?: string;
  speakerRoles?: Record<string, string>;
  actionItems?: string[];
  // Conversation flags as object
  conversationFlags?: {
    satisfaction: boolean;
    confusion: boolean;
    urgency: boolean;
    frustration: boolean;
    abusive: boolean;
  };
}

export const mockCalls: CallData[] = [
  {
    id: "CALL-001",
    dateTime: "2023-05-19T09:30:00",
    counselor: "Priya Sharma",
    studentNumber: "ST20220134",
    duration: "12:40",
    sentiment: "positive",
    flags: ["satisfaction"],
    infoAccuracy: 98,
    transcript: "Student: Hi, I'm calling about the admission requirements for the MBA program.\n\nCounselor: Hello! I'd be happy to help you with information about our MBA program. The basic requirements include a bachelor's degree with at least 60% marks, GMAT or CAT scores, and work experience is preferred but not mandatory.\n\nStudent: That's great. How long is the program?\n\nCounselor: The MBA program is a two-year full-time course. We also offer specializations in Finance, Marketing, HR, and Operations.\n\nStudent: Perfect! What about the fee structure?\n\nCounselor: The total fee for the two-year program is ₹12,50,000, which can be paid in four semester installments. We also have scholarship options for meritorious students.\n\nStudent: That sounds good. How can I apply?\n\nCounselor: You can apply online through our university website. The application deadline for the upcoming batch is June 30th. Would you like me to send you the direct link?\n\nStudent: Yes, please do. Thank you so much for your help!\n\nCounselor: You're welcome! I'll send the link to your email. Is there anything else I can assist you with today?\n\nStudent: No, that's all. Thank you again.",
    summary: "Student inquired about MBA program admission requirements, duration, fees, and application process. All information provided accurately. Student expressed satisfaction with the conversation.",
    missedInfo: [],
    incompleteInfo: []
  },
  {
    id: "CALL-002",
    dateTime: "2023-05-19T11:15:00",
    counselor: "Rahul Kapoor",
    studentNumber: "**********",
    duration: "08:15",
    sentiment: "negative",
    flags: ["frustration", "confusion"],
    infoAccuracy: null, // Test null accuracy
    transcript: "Student: I've been trying to get information about the scholarship deadline for two days now!\n\nCounselor: I apologize for the inconvenience. How can I help you today?\n\nStudent: I need to know when the last date for scholarship application is. The website says May 15th but someone from your office told me it's May 30th.\n\nCounselor: Let me check that for you... I believe the deadline is May 30th.\n\nStudent: You believe? Don't you have the correct information? This is really frustrating.\n\nCounselor: I apologize for the confusion. Let me double-check... Actually, for merit scholarships, the deadline is May 15th, and for need-based scholarships, it's May 30th.\n\nStudent: That's not what your website says! It doesn't mention different deadlines.\n\nCounselor: I'm sorry about that. The website information might not be updated. I'll make a note of this and have it corrected.\n\nStudent: This is exactly why I've been calling repeatedly. I need accurate information to plan my finances.\n\nCounselor: I completely understand your frustration. To confirm, which scholarship program are you interested in applying for?\n\nStudent: The merit scholarship. I have a 90% aggregate.\n\nCounselor: In that case, I would recommend submitting your application by May 15th to be safe. However, I will check with the scholarship committee and call you back with the confirmed deadline.\n\nStudent: Please do, and make sure it's the correct information this time.",
    summary: "Student called regarding scholarship deadlines. There was confusion about different deadlines for different scholarship types. The counselor provided partially incorrect information initially but later clarified. Student expressed frustration about inconsistent information.",
    missedInfo: ["Correct scholarship deadline for merit scholarships", "Process to appeal if deadline is missed"],
    incompleteInfo: [
      {
        provided: "Merit scholarship deadline is May 15th",
        complete: "Merit scholarship deadline is May 15th, but applications can be submitted until May 20th with a late fee of ₹500"
      },
      {
        provided: "Website information might not be updated",
        complete: "Website information is updated weekly on Fridays, and the current version shows deadlines effective from March 2023"
      }
    ]
  },
  {
    id: "CALL-003",
    dateTime: "2023-05-20T10:00:00",
    counselor: "Amit Singh",
    studentNumber: "**********",
    duration: "05:22",
    sentiment: "neutral",
    flags: ["urgency"],
    infoAccuracy: 85,
    transcript: "Student: Hello, I need to urgently submit my transcript request for my university application abroad. The deadline is tomorrow!\n\nCounselor: I understand this is urgent. For transcript requests, you need to fill out Form-TR01 and submit it to the Registrar's office.\n\nStudent: How long does it take to process?\n\nCounselor: Usually it takes 3-5 working days.\n\nStudent: But I need it by tomorrow! Is there any expedited process?\n\nCounselor: For urgent requests, there is a same-day processing option with an additional fee of ₹1000.\n\nStudent: That's fine. Where do I make the payment?\n\nCounselor: You can make the payment online through the student portal or directly at the accounts office.\n\nStudent: Can I get a digital copy sent to my email as well?\n\nCounselor: Yes, we can send a digital copy to your registered email address, but please note that some universities only accept physical copies with the university seal.\n\nStudent: I'll check with my university. Thanks for the information.",
    summary: "Student needed urgent transcript processing for an international university application. Information was provided about the standard and expedited processes. Student was concerned about the tight deadline but satisfied with the expedited option.",
    missedInfo: ["Exact office hours for the Registrar", "Number of copies allowed per request"],
    incompleteInfo: [
      {
        provided: "Same-day processing with additional fee of ₹1000",
        complete: "Same-day processing with additional fee of ₹1000, available only for requests submitted before 2 PM on working days"
      }
    ]
  },
  {
    id: "CALL-004",
    dateTime: "2023-05-20T13:45:00",
    counselor: "Ananya Mishra",
    studentNumber: "**********",
    duration: "10:05",
    sentiment: "positive",
    flags: ["satisfaction"],
    infoAccuracy: 95,
    transcript: "Student: Hi, I'm interested in the Computer Science program and would like to know more about the curriculum.\n\nCounselor: Hello! I'd be happy to help you with information about our Computer Science program. The curriculum is designed to cover fundamental concepts as well as advanced topics in areas like artificial intelligence, data science, and cybersecurity.\n\nStudent: That sounds interesting. What kind of projects would I work on?\n\nCounselor: During the four-year program, you'll work on several projects including a minor project in your second year and a major project in your final year. We also have collaborations with tech companies for industry-relevant projects.\n\nStudent: Are there any internship opportunities?\n\nCounselor: Yes, we have a dedicated placement cell that helps students secure internships. In fact, it's mandatory to complete at least one internship before graduation.\n\nStudent: What about faculty? Do they have industry experience?\n\nCounselor: Most of our faculty members have both academic and industry experience. Several of them have worked with companies like Microsoft, Google, and IBM before joining academia.\n\nStudent: That's great to hear! What are the career prospects after this program?\n\nCounselor: Our graduates have excellent placement records in software development, data analysis, cybersecurity, and other IT roles. Many also pursue higher education at prestigious universities worldwide.\n\nStudent: Thank you for the detailed information. This is exactly what I was looking for.\n\nCounselor: You're welcome! Would you like me to send you the detailed curriculum via email?\n\nStudent: Yes, please. That would be very helpful.",
    summary: "Student inquired about the Computer Science program curriculum, projects, internships, faculty qualifications, and career prospects. All information was provided comprehensively and accurately. Student expressed satisfaction with the detailed information.",
    missedInfo: [],
    incompleteInfo: []
  },
  {
    id: "CALL-005",
    dateTime: "2023-05-21T09:30:00",
    counselor: "Vikram Mehta",
    studentNumber: "ST20230022",
    duration: "07:30",
    sentiment: "neutral",
    flags: ["confusion"],
    infoAccuracy: 80,
    transcript: "Student: I'm a bit confused about the hostel allocation process for the new semester.\n\nCounselor: I can help clarify that. Hostel allocation is based on a first-come-first-serve basis for new students and a merit-based system for returning students.\n\nStudent: I'm a returning student. How is the merit calculated?\n\nCounselor: Merit is calculated based on your academic performance in the previous semester and your participation in extracurricular activities.\n\nStudent: Is there a separate application for the hostel?\n\nCounselor: Yes, you need to fill out the hostel application form available on the student portal. The deadline is usually one month before the semester starts.\n\nStudent: What about the payment? Do we pay monthly or for the whole semester?\n\nCounselor: Hostel fees are charged on a semester basis, but you can opt for a payment plan to pay in two installments.\n\nStudent: And is there a specific time when the allocations are announced?\n\nCounselor: Allocations are typically announced two weeks before the semester starts, but this can vary. It's best to keep checking the student portal for updates.\n\nStudent: I see. Thanks for explaining. I'm still not entirely clear about the merit system, but I'll fill out the application.",
    summary: "Student inquired about the hostel allocation process, particularly for returning students. Information about application process, payment options, and announcement timeline was provided. The student remained somewhat confused about the merit calculation system.",
    missedInfo: ["Detailed breakdown of merit calculation", "Contact information for hostel administration"],
    incompleteInfo: [
      {
        provided: "Merit is calculated based on academic performance and extracurricular activities",
        complete: "Merit is calculated based on 70% academic performance (CGPA), 20% extracurricular activities (points system), and 10% community service hours"
      },
      {
        provided: "Allocations are announced two weeks before semester starts",
        complete: "Allocations are announced two weeks before semester starts, with a waiting list published 3 days later for any cancellations"
      }
    ]
  },
  {
    id: "CALL-006",
    dateTime: "2023-05-21T14:20:00",
    counselor: "Rohit Sharma",
    studentNumber: "ST20210088",
    duration: "04:15",
    sentiment: "negative",
    flags: ["frustration"],
    infoAccuracy: undefined, // Test undefined accuracy
    transcript: "Student: I'm having issues with my fee payment. The online portal keeps showing an error.\n\nCounselor: I'm sorry to hear that. What error message are you seeing?\n\nStudent: It says 'Transaction failed. Please try again later.' I've tried multiple times over the past two days.\n\nCounselor: The payment gateway might be experiencing issues. Have you tried using a different browser or device?\n\nStudent: Yes, I've tried everything. This is really frustrating as the deadline is approaching.\n\nCounselor: I understand your concern. As an alternative, you can make a direct bank transfer to the university account.\n\nStudent: Can you provide the account details?\n\nCounselor: Actually, I don't have those details on hand. You would need to contact the accounts department for that information.\n\nStudent: So you're telling me to contact yet another department? This is exactly what I've been doing for days!\n\nCounselor: I apologize for the inconvenience. Let me try to get those details for you... Actually, I do have the account number but not the IFSC code.\n\nStudent: This is ridiculous. I need complete information to make the transfer.\n\nCounselor: You're right, and I apologize. Let me transfer you to someone in the accounts department who can provide complete information.\n\nStudent: Fine, but this should have been handled better.",
    summary: "Student faced issues with online fee payment and was seeking alternative payment methods. The counselor was unable to provide complete bank details for direct transfer, causing frustration. The call was eventually transferred to the accounts department.",
    missedInfo: ["Complete bank details for direct transfer", "Expected resolution time for online portal issues", "Late payment penalty information"],
    incompleteInfo: [
      {
        provided: "You can make direct bank transfer to university account",
        complete: "You can make direct bank transfer to university account - Account Name: IILM University, Account Number: *********, IFSC: HDFC0001234, Branch: Greater Noida"
      }
    ]
  },
  {
    id: "CALL-007",
    dateTime: "2023-05-22T11:00:00",
    counselor: "Neha Gupta",
    studentNumber: "**********",
    duration: "09:50",
    sentiment: "positive",
    flags: ["satisfaction"],
    infoAccuracy: 90,
    transcript: "Student: Hello, I'm interested in transferring from another university to your Computer Science program.\n\nCounselor: Good morning! I'd be happy to guide you through the transfer process. May I know which university you're currently enrolled in and which year of the program you're in?\n\nStudent: I'm currently in my second year at Delhi University, studying Computer Science.\n\nCounselor: Thank you for that information. For transfer students, we evaluate applications based on your current academic standing and the compatibility of the curriculum. You would need to submit your current university transcripts, a statement of purpose, and a transfer application form.\n\nStudent: Would I lose any credits in the transfer?\n\nCounselor: We have a credit transfer committee that evaluates each course you've completed against our curriculum. Typically, students can transfer up to 50% of the total credits required for the degree, but this varies case by case.\n\nStudent: That sounds reasonable. What's the application deadline?\n\nCounselor: For the upcoming fall semester, the transfer application deadline is June 15th. For the spring semester, it's November 30th.\n\nStudent: Perfect. And is there an application fee?\n\nCounselor: Yes, there's a non-refundable application fee of ₹2,500.\n\nStudent: Thank you so much for this information. It's been very helpful.\n\nCounselor: You're welcome! Would you like me to email you the transfer application form and a detailed checklist?\n\nStudent: Yes, that would be great. My <NAME_EMAIL>.\n\nCounselor: I'll send that to you right away. If you have any more questions during the application process, feel free to call us again.",
    summary: "Student inquired about transferring from Delhi University to IILM's Computer Science program. Information about the application process, credit transfer, deadlines, and fees was provided accurately. Student was satisfied with the guidance received.",
    missedInfo: ["Specific documents needed from Delhi University", "Contact information for the credit transfer committee"],
    incompleteInfo: []
  },
  {
    id: "CALL-008",
    dateTime: "2023-05-22T15:40:00",
    counselor: "Sanjay Patel",
    studentNumber: "**********",
    duration: "03:25",
    sentiment: "neutral",
    flags: [],
    infoAccuracy: 100,
    transcript: "Student: Hi, I just wanted to confirm the date for the orientation program.\n\nCounselor: Hello! The orientation program for new students is scheduled for July 25th, from 9 AM to 5 PM.\n\nStudent: Do I need to bring anything specific?\n\nCounselor: You should bring your admission letter, photo ID, and it's advisable to bring a notebook and pen. All other materials will be provided during the orientation.\n\nStudent: Is attendance mandatory?\n\nCounselor: Yes, attendance is mandatory as important information about academic policies, campus facilities, and initial course registrations will be covered.\n\nStudent: Got it. Thanks for confirming.\n\nCounselor: You're welcome! If you have any other questions, feel free to reach out.",
    summary: "Student called to confirm the date for the orientation program. Information about the date, time, items to bring, and mandatory attendance was provided accurately.",
    missedInfo: [],
    incompleteInfo: []
  },
  {
    id: "CALL-009",
    dateTime: "2023-05-23T10:10:00",
    counselor: "Divya Malhotra",
    studentNumber: "ST20220134",
    duration: "06:35",
    sentiment: "negative",
    flags: ["frustration", "urgency"],
    infoAccuracy: 75,
    transcript: "Student: I need to discuss an issue with my scholarship disbursement. It's been two months and I haven't received it yet.\n\nCounselor: I'm sorry to hear that. Can you please provide your student ID so I can look into this?\n\nStudent: It's ST20220134.\n\nCounselor: Thank you. Let me check the status... I can see that your scholarship application was approved, but there seems to be a delay in the disbursement.\n\nStudent: That's what I've been told for weeks now! I need this money for my tuition payment which is due next week.\n\nCounselor: I understand your urgency. The delay might be due to the verification process with the bank.\n\nStudent: Can you at least tell me when exactly I will receive it? I've been getting different answers from different people.\n\nCounselor: Based on similar cases, it typically takes about 2-3 weeks for the disbursement to process after approval.\n\nStudent: But it's already been more than that! This is affecting my studies as I'm constantly worried about finances.\n\nCounselor: Let me escalate this issue to the Finance Department. They should be able to expedite the process considering your situation.\n\nStudent: I hope so. I've already escalated this multiple times.\n\nCounselor: I understand your frustration. I'll personally follow up on this and call you back by tomorrow with a concrete update.\n\nStudent: Please do. I really need this resolved as soon as possible.",
    summary: "Student called about a delayed scholarship disbursement that was affecting their ability to pay upcoming tuition. The counselor identified the approval status but couldn't provide a concrete timeline for disbursement. The issue was escalated with a promise to follow up.",
    missedInfo: ["Exact reason for the disbursement delay", "What documents might be needed to expedite the process", "Alternative payment options for tuition while waiting for scholarship"],
    incompleteInfo: [
      {
        provided: "It typically takes 2-3 weeks for disbursement after approval",
        complete: "It typically takes 2-3 weeks for disbursement after approval, but can be expedited to 5-7 business days if supporting documents are provided to the Finance Department"
      }
    ]
  },
  {
    id: "CALL-010",
    dateTime: "2023-05-23T16:05:00",
    counselor: "Raj Kumar",
    studentNumber: "ST20210067",
    duration: "11:20",
    sentiment: "positive",
    flags: ["satisfaction"],
    infoAccuracy: 95,
    transcript: "Student: Hello, I'm calling to inquire about the study abroad programs offered by IILM.\n\nCounselor: Hello! I'd be happy to help you with information about our study abroad programs. We have semester exchange programs, summer schools, and dual degree options with various partner universities worldwide.\n\nStudent: That sounds interesting. Can you tell me more about the semester exchange program?\n\nCounselor: Our semester exchange program allows you to study for one or two semesters at a partner university. We have partnerships with universities in the US, UK, Australia, France, Germany, Singapore, and many other countries. Your credits earned abroad will be transferred back to your IILM degree.\n\nStudent: What are the eligibility requirements?\n\nCounselor: You need to have completed at least one year at IILM with a minimum CGPA of 7.5. You also need to meet the language proficiency requirements of the host university, typically IELTS or TOEFL for English-speaking countries.\n\nStudent: Are there any scholarships available for these programs?\n\nCounselor: Yes, we have merit-based scholarships that cover partial tuition fees. Additionally, some partner universities offer scholarships specifically for exchange students from IILM.\n\nStudent: That's great! How and when should I apply?\n\nCounselor: The application process begins about 6 months before the intended semester abroad. For the fall semester (August-December), you should apply by February, and for the spring semester (January-May), you should apply by July.\n\nStudent: Thank you so much for this information. I'm definitely interested in applying for the next fall semester.\n\nCounselor: That's excellent! I'd be happy to send you a detailed brochure and application guide via email. May I have your email address?\n\nStudent: Yes, it's <EMAIL>.\n\nCounselor: Perfect. I'll send that information right away. We also have an information session coming up next week if you'd like to learn more and meet students who have participated in these programs.\n\nStudent: That would be very helpful. Please include details about that session as well.\n\nCounselor: Absolutely, I'll include all the details in the email. Is there anything else you'd like to know about our study abroad programs?\n\nStudent: No, this is all very helpful. Thank you for your time.",
    summary: "Student inquired about study abroad programs at IILM. Comprehensive information was provided about semester exchange programs, eligibility criteria, scholarships, and application deadlines. Student expressed interest in applying for the next fall semester.",
    missedInfo: ["Specific list of partner universities", "Cost estimates for different programs"],
    incompleteInfo: []
  },
  {
    id: "CALL-011",
    dateTime: "2023-05-24T09:20:00",
    counselor: "Priya Sharma",
    studentNumber: "**********",
    duration: "07:45",
    sentiment: "neutral",
    flags: ["confusion"],
    infoAccuracy: 85,
    transcript: "Student: Hi, I'm a bit confused about the elective selection process for the next semester.\n\nCounselor: I'd be happy to help clarify the elective selection process. When are you scheduled to select your electives?\n\nStudent: According to the email, it starts next Monday, but I'm not sure how the system works.\n\nCounselor: The elective selection is done through the student portal. You'll receive a time slot during which you can log in and select your preferred electives in order of priority.\n\nStudent: How many electives am I supposed to select?\n\nCounselor: For your program, you need to select three electives per semester, plus two alternates in case your preferred electives are full.\n\nStudent: What happens if I miss my time slot?\n\nCounselor: If you miss your assigned time slot, you can still make selections during the open selection period, which is usually the last two days of the selection week. However, many popular electives might be filled by then.\n\nStudent: I see. Are there any prerequisites for selecting certain electives?\n\nCounselor: Yes, some advanced electives have prerequisites. You can check these in the course catalog on the student portal. The system will also prevent you from selecting electives for which you haven't completed the prerequisites.\n\nStudent: That makes sense. So once I select my electives, are they confirmed immediately?\n\nCounselor: Not necessarily. After the selection period ends, the system allocates electives based on various factors including your priority choices, academic standing, and seat availability. You'll receive a final confirmation of your electives about a week after the selection period ends.\n\nStudent: I see. Thanks for explaining. I'm still a bit confused about the priority system, but I think I understand the basic process now.",
    summary: "Student inquired about the elective selection process, including timing, number of electives to select, consequences of missing the time slot, prerequisites, and confirmation process. Most information was provided accurately, but there was some confusion regarding the priority system.",
    missedInfo: ["Detailed explanation of how the priority system works", "How to check for seat availability in real-time"],
    incompleteInfo: []
  },
  {
    id: "CALL-012",
    dateTime: "2023-05-24T14:30:00",
    counselor: "Amit Singh",
    studentNumber: "**********",
    duration: "05:55",
    sentiment: "negative",
    flags: ["frustration"],
    infoAccuracy: null, // Test string accuracy from API (will be treated as N/A)
    transcript: "Student: I'm calling because I've been trying to get my graduation certificate for weeks now, and nobody seems to be able to help me.\n\nCounselor: I'm sorry to hear about your difficulty. Let me try to assist you. Have you applied for the certificate through the registrar's office?\n\nStudent: Yes, I submitted the application form a month ago, paid the fee, and was told it would take 2 weeks. It's been more than that, and every time I inquire, I get different answers.\n\nCounselor: I apologize for this inconvenience. Let me check the status for you. Can you provide your student ID?\n\nStudent: It's **********.\n\nCounselor: Thank you. Let me check... I see that your application is in the processing stage. There might be a delay due to the high volume of requests.\n\nStudent: That's what I was told two weeks ago! I need this certificate for my job application, and the deadline is next week.\n\nCounselor: I understand your urgency. In such cases, you can apply for an expedited process.\n\nStudent: No one mentioned this to me before! How do I do that?\n\nCounselor: You would need to submit an expedited processing request form along with an additional fee of ₹1,500.\n\nStudent: So I have to pay more because of the university's delay? That doesn't seem fair.\n\nCounselor: I understand your frustration. Let me see if I can escalate this without the expedited fee given the circumstances... Actually, it seems there might be an issue with your clearance from the library department.\n\nStudent: What? I returned all books and got the clearance before submitting the application!\n\nCounselor: I see the confusion now. It appears the library clearance was received, but it wasn't updated in the system. I'll make a note of this and ensure it's corrected.\n\nStudent: This is extremely frustrating. I've been given incorrect information multiple times.\n\nCounselor: I sincerely apologize for the confusion and frustration this has caused. I will personally follow up on this and ensure your certificate is processed by early next week.",
    summary: "Student called about a delayed graduation certificate needed for a job application. Initial information about the cause of delay was incorrect. After investigation, it was discovered that a library clearance had been received but not updated in the system. There was confusion about the expedited processing option.",
    missedInfo: ["Correct status of library clearance", "Actual timeline for certificate issuance", "Alternative documentation options for job applications"],
    incompleteInfo: [
      {
        provided: "Expedited processing with additional fee of ₹1,500",
        complete: "Expedited processing with additional fee of ₹1,500, which guarantees certificate issuance within 3 business days, available only for urgent cases with supporting documentation"
      }
    ]
  }
];

// Generate aggregated data for dashboard metrics
export const getDashboardData = () => {
  // Count sentiments
  const sentimentCounts = mockCalls.reduce((acc, call) => {
    acc[call.sentiment] = (acc[call.sentiment] || 0) + 1;
    return acc;
  }, {} as Record<string, number>);

  // Calculate percentages
  const totalCalls = mockCalls.length;
  const sentimentDistribution = Object.entries(sentimentCounts).map(([sentiment, count]) => ({
    name: sentiment.charAt(0).toUpperCase() + sentiment.slice(1),
    value: Math.round((count / totalCalls) * 100)
  }));

  // Count flags
  const flagTypes = ['frustration', 'confusion', 'urgency', 'satisfaction', 'abusive'];
  const flagCounts = flagTypes.map(flag => ({
    name: flag.charAt(0).toUpperCase() + flag.slice(1),
    count: mockCalls.filter(call => call.flags.includes(flag as any)).length
  }));

  // Calculate average accuracy (only for calls with valid accuracy values)
  const callsWithAccuracy = mockCalls.filter(call => call.infoAccuracy !== null && call.infoAccuracy !== undefined);
  const averageAccuracy = callsWithAccuracy.length > 0
    ? Math.round(
        callsWithAccuracy.reduce((sum, call) => sum + (call.infoAccuracy || 0), 0) / callsWithAccuracy.length
      )
    : 0;

  // Calculate average duration
  const totalMinutes = mockCalls.reduce((sum, call) => {
    const [mins, secs] = call.duration.split(':').map(Number);
    return sum + mins + secs / 60;
  }, 0);
  const averageDuration = (totalMinutes / mockCalls.length).toFixed(1);

  // Mock sentiment trend data
  const sentimentTrend = [
    { date: '05/19', score: 0.2 },
    { date: '05/20', score: 0.4 },
    { date: '05/21', score: -0.1 },
    { date: '05/22', score: 0.5 },
    { date: '05/23', score: 0.0 },
    { date: '05/24', score: -0.2 },
    { date: '05/25', score: 0.3 },
  ];

  return {
    sentimentDistribution,
    flagCounts,
    accuracyScore: {
      current: averageAccuracy,
      previous: averageAccuracy - 3, // Mock previous week for trend
    },
    callVolume: {
      total: totalCalls,
      averageDuration
    },
    sentimentTrend
  };
};
